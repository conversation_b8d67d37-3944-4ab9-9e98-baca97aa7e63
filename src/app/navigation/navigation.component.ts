import { Component, ChangeDetectorRef, OnInit, ViewChild, Input, HostListener } from "@angular/core";
import { BreakpointObserver, MediaMatcher } from "@angular/cdk/layout";
import { MenuItemService, ShareDataService } from '../_services';
import { GlobalsService } from "../_services/globals.service";
import { Router } from "@angular/router";
import { AuthService } from "../_services";
import { User } from "../_models";
import { MatDialog, MatSelect, MatSidenav } from "@angular/material";
import { SimpleDialogComponent } from "../_dialogs/simple-dialog/simple-dialog.component";
import { NotificationService } from '../_services/notification.service';
import { InventoryMasterDataService } from './../_services/inventory-master-data.service';
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { ReplaySubject, Subject } from 'rxjs';
import {takeUntil } from 'rxjs/operators';
import { MatOption } from '@angular/material';
import { UtilsService } from "../_utils/utils.service";
import { TimeOutService } from "../_services/time-out.service";

@Component({
  selector: "app-navigation",
  templateUrl: "./navigation.component.html",
  styleUrls: ["./navigation.component.scss"],
})
export class NavigationComponent implements OnInit {
  navLinks;
  links: any = [];
  rolesList: any = [];
  title = "Digitory";
  isLoggedIn: boolean = false;
  mobileQueryListener: () => void;
  mobileQuery: MediaQueryList;
  user: User;
  cardDesc: any;
  purOrderTitle = "Purchase Orders";
  inventoryListTitle = "Inventory List";
  closingStockTitle = "Closing Stock";
  notifications: any[];
  tenantId: any;
  userDetails: any;
  logoUrl: string;
  versionNumber: string = "v3.34.1";
  @ViewChild(MatSidenav)
  sidenav! : MatSidenav
  public vendorsBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public VendorBank: any[] = [];
  public vendorFilterCtrl: FormControl = new FormControl();
  public userType: FormControl = new FormControl();
  
  protected _onDestroy = new Subject<void>();
  isDirectValueOfBranch: boolean = false;
  @ViewChild('allSelected') private allSelected: MatOption;
  isShowNotify : boolean = false;
  role = ['CRM', 'superadmin', 'SuperAdmin']
  @Input() message: string = 'A new version of our software is now available! Please update to enjoy the latest features and improvements';
  @Input() messageMaintanance: string = 'Attention - server maintenance will occur from 12:00 AM to 1:00 AM (24/6/2024). Inventory system will be down during this time';
  @Input() showBanner: boolean = false;
  @Input() alert: boolean = false;
  showMaintanance = false 
  constructor(
    private observer: BreakpointObserver,
    private menuItems: MenuItemService,
    private cd: ChangeDetectorRef,
    private media: MediaMatcher,
    private router: Router,
    private auth: AuthService,
    private dialog: MatDialog,
    private notifyService: NotificationService,
    private inventoryMasterDataService: InventoryMasterDataService,
    private sharedData: ShareDataService,
    private fb: FormBuilder,
    private utils: UtilsService,
    private sessionTimeoutService: TimeOutService
  ) {
    this.user = this.auth.getCurrentUser();
    this.tenantId = sessionStorage.getItem('tanetId')
    let tempLocalUser: any = sessionStorage.getItem('user')
    this.userDetails = JSON.parse(tempLocalUser);
    this.menuItems.notificationDta.subscribe(notification => this.notifications = notification);
    this.responsiveSideNav();
    this.auth.getRolesList({ tenantId: this.user.tenantId,restaurantId: this.user.restaurantAccess[0].restaurantIdOld }).subscribe((data) => {
      this.logoUrl = data.tenantDetails.logo;
      if (this.versionNumber !== data['version']){
        this.showBanner = true
      }
      if (data.parLevel === true) {
        this.alert = true;
        this.redirect();
      }
      data.rolesList.forEach((element) => {
        if (element.role == this.user.role) {
          this.rolesList = element;
        }
      });

      if(data['timeOut']){
        this.sharedData.sendTimeOutData(data['timeOut']);
        this.sessionTimeoutService.start();
      }

      sessionStorage.setItem(GlobalsService.accessData, JSON.stringify(data['access']));
      sessionStorage.setItem('access', data['access'].settings);

      Object.entries(this.rolesList.modules).forEach((element) => {
        let pages: any = element[1];
        let childrenList :any[] = [];
        let obj = {};
        pages.forEach((page) => {
          let childObj: any = { path: page, name: page };
          childrenList.push(childObj);
        });
        obj = { path: null, name: element[0], children: childrenList };
        this.links.push(obj);
      });
      sessionStorage.setItem('moduleList', JSON.stringify(this.links));
    });
    this.cardDesc = "";
    this.cardDesc += this.user.role;
    this.navLinks = this.links;
  }

  redirect() {
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Alert',
        msg: 'We noticed below-par-level stock for the current location.',
        ok: function () {
          this.router.navigate(['/home/<USER>'], { queryParams: { type: 'alert' } });
          this.alert = false;
        }.bind(this)
      }
    });
  }

  closeBanner() {
    this.showMaintanance = false;
  }
  ngOnInit() {
    this.getAccessData();
    this.VendorBank = this.user.restaurantAccess 
    this.vendorsBanks.next(this.VendorBank.slice());
    this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.vendorfilterBanks();
    });
    // this.getInventoryOfTenantId();
    this.toggleAllSelection(true);
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  getInventoryOfTenantId() {
    let objTenant: any = {
      "tenantId": this.tenantId,
      "restaurantId": this.userDetails.restaurantId,
      "userRole": this.userDetails.role
    }
    this.inventoryMasterDataService.getInventoryForTenantId(objTenant).subscribe((response: any) => {
      if (response.result == 'success') {
        let tempArr: any[] = response.data;
        sessionStorage.setItem('inventoryData', JSON.stringify(tempArr))
      }
    }, (error) => {
      console.log("error", error)
    });
  }

  ngAfterViewInit() {
    this.observer.observe(['(max-width: 1200px)']).subscribe((res) => {
      if (res.matches) {
        this.sidenav.mode = 'over';
        this.sidenav.close();
      } else {
        this.sidenav.mode = 'side';
        this.sidenav.open();
      }
    });
  }

  toggleMenu(index) {
    this.navLinks[index].panelOpenState = !this.navLinks[index].panelOpenState;
  }

  private filterLinks(links) {
    let filteredLinks: any[] = [];
    links.forEach((link) => {
      let filteredRoles = this.user.role.filter((item) =>
        link.roles.includes(item)
      );
      if (filteredRoles.length > 0) {
        filteredLinks.push(link);
        if (link.hasOwnProperty("children")) {
          link.children = this.filterLinks(link.children);
        }
      }
    });
    return filteredLinks;
  }

  deleteNotification(notificationId) {
    let obj = this.user;
    obj["notificationId"] = notificationId;
    this.menuItems.deleteNotification(obj).subscribe(res => {
      if (res['result'] == 'success') {
        this.retrieveNotification();
      }
    }, err => { console.log( err )
    });
  }

  retrieveReport(this, reportName, currentUser) {
    let obj = currentUser;
    obj['type'] = reportName;
    this.menuItems.retrieveReport(obj).subscribe(res => {
      if (res['result'] == 'success') {
        window.open('data:application/vnd.ms-excel;base64,' + res.eFile);
      }
      else {
        this.utils.snackBarShowError('file does not exist. Please try again later');
      }
    }, err => {
      console.log(
        err
      )
    });
  }

  download(obj) {
    let currentUser = this.user;
    var requestDate = obj['details']['requestDate'].replace(' ', '')
    requestDate = requestDate.replace('/', '-')
    requestDate = requestDate.replace('/', '-')
    requestDate = requestDate.replace(' ', '')
    var fullName = obj['details']['type'] + '|' + requestDate + '|' + obj['reportNo'] + '.xlsx'
    this.retrieveReport(fullName, currentUser);
  }


  retrieveNotification() {
    let obj = this.user;
    this.menuItems.retrieveNotification(obj).subscribe(res => {
      if (res['result'] == 'success') {
        this.notifications = res.data;
      }
    }, err => {
      console.log(
        err
      )
    });
  }

  private responsiveSideNav() {
    this.mobileQuery = this.media.matchMedia(
      "(min-width: 768px) and (max-width: 1024px)"
    );
    this.mobileQueryListener = () => this.cd.detectChanges();
    this.mobileQuery.addListener(this.mobileQueryListener);
  }

  logout() {
    this.dialog.open(SimpleDialogComponent, {
      data: {
        msg: "Are you sure you want to logout?",
        title: "Logout",
        ok: function () {
          sessionStorage.removeItem(GlobalsService.user);
          this.router.navigate(["/login"]);
        }.bind(this),
      },
    });
  }

  changePassword(){
    this.dialog.open(SimpleDialogComponent, {
      data: {
        msg: "Are you sure you want to change password?",
        title: "Change Password",
        ok: function () {
          this.router.navigate(["/login"]);
        }.bind(this),
      },
    });
  }

  change(){
    let obj = {
      "tenantId": this.user.tenantId,
      "email": this.user.email,
      "oldPassword": '1',
      "newPassword": '2',
      "reEnterPassword": '3',
    }
    this.menuItems.changePassword(obj).subscribe(res => {
      if (res['result'] == 'success') {
        
      }
      else {
        
      }
    }, err => {
      console.log(
        err
      )
    });

  }

  navClicked(link) {
    this.utils.snackBarShowSuccess(link);
    switch (link) {
      case GlobalsService.logout:
        sessionStorage.removeItem(GlobalsService.user);
        this.router.navigate(["/login"]);
        break;
      default:
    }
  }

  Settings(){
    this.router.navigate(['/home/<USER>']);
  }

  protected vendorfilterBanks() {
    if (!this.VendorBank) {
      return;
    }
    let search = this.vendorFilterCtrl.value;
    if (!search) {
      this.vendorsBanks.next(this.VendorBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.vendorsBanks.next(
      this.VendorBank.filter(VendorBank => VendorBank.branchName.toLowerCase().indexOf(search) > -1)
    );
  }

  toggleAllSelection(manual = false) {
    if (this.allSelected && this.allSelected.selected ) {
      this.userType.patchValue([...this.user.restaurantAccess.map(item => item.branchName), 1]);
    } else if (manual){
      // this.userType.patchValue([...this.user.restaurantAccess.map(item => item.branchName), 1]);
      const singleData = [this.user.restaurantAccess[0]]
      this.userType.patchValue([...singleData.map(item => item.branchName)]);
    }else {
      this.userType.patchValue([]);
    }
    this.filterByBranches()
  }

  filterByBranches(){
    let selectedBranches = []
    for (let i = 0; i < this.userType.value.length; i++) {
      let selected = this.user.restaurantAccess.filter(obj =>{ return obj.branchName == this.userType.value[i]})
      if (selected.length >0){
        selectedBranches.push(selected[0])
      }
      if (i == this.userType.value.length - 1){
        this.sharedData.sharedBranchArr(selectedBranches);
      }
    }
  }

  showNotifications(){
    this.isShowNotify = true
  }

  @HostListener('document:keydown.control.shift.r', ['$event'])
  onCtrlShiftR(event: KeyboardEvent) {
    // Prevent the default action of the keyboard event to avoid unwanted browser behavior
    event.preventDefault();

    // Refresh the page programmatically
    window.location.reload();
  }

  refreshPage() {
    // Dispatch a synthetic keyboard event to simulate pressing Ctrl + Shift + R
    const event = new KeyboardEvent('keydown', {
      key: 'r',
      code: 'KeyR',
      ctrlKey: true,
      shiftKey: true
    });
    
    // Dispatch the event on the document object
    document.dispatchEvent(event);
  }

  openSetting(){
    this.router.navigate(['/home/<USER>'])
  }

  getAccessData(){
    let data = sessionStorage.getItem(('access'))
  }

  checkUserRole() {
    let data : any = this.user.role
    return this.role.includes(data);
  }
}
