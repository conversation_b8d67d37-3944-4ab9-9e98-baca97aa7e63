import { Component, OnInit, ViewChild } from "@angular/core";
import { BranchTransferService, AuthService, PurchasesService } from "../_services";
import { GlobalsService } from "../_services/globals.service";
import { ShareDataService } from '../_services/share-data.service';
import { Router } from "@angular/router";
import { MatTableDataSource, MatPaginator } from "@angular/material";
import { UtilsService } from "../_utils/utils.service";
import { NotificationService } from '../_services/notification.service';
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { takeUntil } from "rxjs/operators";
import { Subject } from "rxjs";
@Component({
  selector: 'app-spoilage',
  templateUrl: './spoilage.component.html',
  styleUrls: ['./spoilage.component.scss', "./../../common-dark.scss"]
})
export class SpoilageComponent implements OnInit {
  IndentAreas: string[] = [];
  indentArea: any;
  adjustInvFlag: boolean;
  documentDate = new FormControl();
  all = "ALL";
  adjInvPreview: boolean = false;
  curDataSource: any[];
  adjustWorkAreaInventory = encodeURI(GlobalsService.adjustWorkAreaInventory)
  adjustInventoryUrl = encodeURI(GlobalsService.adjustInventory)
  user: any;
  inventoryItems: any[];
  displayedColumns: string[];
  title: any;
  restaurantId: any;
  showFooter: boolean = false;
  savedItems: any;
  workAreaSelected: boolean = false;
  reqSelected: boolean = false;
  initData: any;
  branchSelected: any;
  multiBranchUser: any;
  categoryList = ['All'];
  subCategoryList = ['All'];
  ItemTypeList = ['All'];
  initSubCategoryList: any = [];
  initCategoryList: any = [];
  subCatList: any = [];
  searchText: any
  filterKeys = { ItemType: 'All', category: 'All', subCategory: 'All' }
  category = new FormControl('', [Validators.required]);
  Subcategory = new FormControl();
  ItemType = new FormControl();
  dataSource: MatTableDataSource<any>;
  totalIndentCost: any = 0;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  private pageSizes: any;
  branches: any[];
  getBranchData: any[];
  today: Date;
  adjustWorkAreaInvForm: FormGroup;
  @ViewChild('table') private _table: any;
  private specialKeys: Array<string> = ['Backspace', 'Tab', 'End', 'Home', 'ArrowLeft', 'ArrowRight', 'Del', 'Delete'];
  private regex: RegExp = new RegExp(/^\d*\.?\d{0,2}$/g);
  selectedWorkArea: any;
  itemtpe: any;
  cat: any;
  subCat: any;
  private unsubscribe$ = new Subject<void>();
  highQnty = false
  constructor(private auth: AuthService, private purchases: PurchasesService,private notifyService: NotificationService,
    private branchTransfer: BranchTransferService,
    private sharedData: ShareDataService,
    private utils: UtilsService,
    private router: Router,
    private fb: FormBuilder) {
    this.user = this.auth.getCurrentUser();
    this.multiBranchUser = this.user.multiBranchUser;
    this.adjustWorkAreaInvForm = this.fb.group({
      branchSelection: [null, Validators.required]
    });

    var windowLocation = window.location.href;
    let windowCurrentUrl = windowLocation.split('/')[4]

      this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
        this.getBranchData = val;
        if(this.getBranchData.length == 0 ){
          this.branches = this.user.restaurantAccess;
        }else if(this.getBranchData.length == 1){        
          const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
          this.adjustWorkAreaInvForm.get('branchSelection').setValue(toSelect);
          this.branches = this.getBranchData
          this.filterByBranch(this.adjustWorkAreaInvForm.value.branchSelection);
        }else{
          this.branches = this.getBranchData
        }
    });
  }
  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
  }
  ngOnInit() {
    if (!this.user.multiBranchUser) {
      this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld
      this.filterByBranch(this.restaurantId)
    }
    this.dataSource = new MatTableDataSource()
    this.displayedColumns = GlobalsService.spoilageColumns;
    let date = new Date();
    date.setHours(0, 0, 0, 0);
    this.today = date ; 
    this.documentDate.setValue(this.today);
  }

  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  selectWorkArea(val) {
    this.adjInvPreview = false
    this.indentArea = val;
    this.workAreaSelected = true;
    this.getBranchInv()
  }

  showAll(){
    this.getBranchInv();
  }

  getBranchInv() {
    let params = {
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId,
      userEmail: this.user.email,
      uId: this.user.mId,
      reqQty: this.highQnty ? false : true
    }

    if(this.indentArea != 'STORE'){
      params["workArea"] = this.indentArea;
    }
    this.branchTransfer.getSpoilageInventoryData(params).subscribe(data => {
        this.initData = data.invItems
        this.inventoryItems = data.invItems
        this.inventoryItems
          .map((item: any) => {
            if (!item.hasOwnProperty('packageName')) {
              item.packageName = item.uom
              item.packageQty = 1
            }
            item.adjustQty = 0
            item.adjustType = 'Dec'
            item.icon = 'arrow_downward'
            return item
          }
          )
      this.dataSource.data = this.inventoryItems
      this.curDataSource = this.dataSource.data

      this.inventoryItems.forEach(item => {
        if (item.category == null) {
          item.category = 'N/A'
        }
        if (item.ItemType == null) {
          item.ItemType = 'N/A'
        }
        if (item.subCategory == null) {
          item.subCategory = 'N/A'
        }
        this.ItemTypeList.push(item.ItemType)
        this.categoryList.push(item.category)
        this.subCategoryList.push(item.subCategory)
      })
      if (this.indentArea == 'STORE') {
        if(this.itemtpe){
          this.selectItemType(this.itemtpe);
        }
        if(this.cat){
          this.selectCategory(this.cat);
        }
        if(this.subCat){
          this.selectSubCat(this.subCat);
        }
      }
      this.categoryList = this.categoryList.filter((k, i, ar) => ar.indexOf(k) === i).sort();
      this.ItemTypeList = this.ItemTypeList.filter((p, q, arrr) => arrr.indexOf(p) === q).sort();
      this.subCategoryList = this.subCategoryList.filter((j, l, arr) => arr.indexOf(j) === l).sort();
      this.initCategoryList = this.categoryList;
      this.initSubCategoryList = this.subCategoryList;
      this.pageSizes = this.utils.getPageSizes(this.inventoryItems);
      this.dataSource.paginator = this.paginator;
      if (this.indentArea != 'STORE') {
        this.dataSource.data = this.inventoryItems.filter(item => Object.keys(item.workArea).includes(this.indentArea))
          .map((item: any) => { item.adjustQty = 0; return item })
        this.curDataSource = this.dataSource.data
        this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
        this.dataSource.paginator = this.paginator;
      }
      if(this.dataSource.data){
        this.dataSource.data = this.dataSource.data
      }

    },
      err => console.error(err))

  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  printToPdf() {
    let inventoryList = {};
    inventoryList['inventoryItems'] = [];
    this.dataSource.data.forEach(function (item) {
      if (item['adjustQty'] > 0) {
        inventoryList['inventoryItems'].push(item);
      }
    });
    if (inventoryList['inventoryItems'].length == 0) {
      this.utils.snackBarShowWarning('please enter indent values');
      return;
    }
    inventoryList['user'] = this.user;
    inventoryList['recipientArea'] = this.indentArea
    this.purchases.printpdfs(inventoryList, 'Indent').subscribe(data => {
      this.purchases.globalPrintPdf(data.eFile);
    });
  }

  exportToExcel() {
    let inventoryList = {}
    inventoryList['indentArea'] = this.indentArea;
    inventoryList['inventoryItems'] = this.dataSource.data;
    inventoryList['user'] = this.user;
    this.purchases.exportToExcel(inventoryList, 'Indent').subscribe(data => {
      window.open('data:text/csv;base64,' + data.eFile);
    });
  }


  filterByBranch(restId) {
    this.restaurantId = restId.restaurantIdOld ? restId.restaurantIdOld : restId;
    this.branchSelected = true
    this.category.setValue('')
    this.dataSource = new MatTableDataSource();
    this.user.restaurantAccess.forEach(element => {
      if (element.restaurantIdOld == this.restaurantId) {
        this.IndentAreas = element.workAreas
        if (element.workAreas == undefined) {
          this.IndentAreas = element.workAreas;
        }
        this.IndentAreas = ['STORE', ...element.workAreas];
      }
    });
  }

  allFilter() {
    let tmp = this.curDataSource
    let prev = this.curDataSource
    Object.keys(this.filterKeys).forEach(element => {
      if (this.filterKeys[element] != 'All') {
        tmp = prev.filter(item =>
          item[element].toLowerCase() === this.filterKeys[element].toLowerCase())
        prev = tmp
      }
    });
    this.dataSource.data = tmp
  }

  selectItemType(itemType) {
    let filteredCategoryList = []
    let filteredSubCategoryList = []
    if (itemType != 'All') {
      let filteredItem = this.curDataSource.filter(item => item['ItemType'].toUpperCase() === itemType.toUpperCase());
      filteredItem.forEach(element => {
        filteredCategoryList.push(element.category)
        filteredSubCategoryList.push(element.subCategory)
      });
      this.categoryList = filteredCategoryList.filter((k, i, ar) => ar.indexOf(k) === i).sort();
      this.subCategoryList = filteredSubCategoryList.filter((k, i, ar) => ar.indexOf(k) === i).sort();
      this.categoryList.splice(0, 0, 'All')
      this.subCategoryList.splice(0, 0, 'All')
      this.subCatList = this.subCategoryList
    }
    else {
      this.categoryList = this.initCategoryList;
      this.subCategoryList = this.initSubCategoryList;
    }
    this.filterKeys = { ItemType: itemType, category: 'All', subCategory: 'All' }
    this.allFilter()
  }

  selectCategory(cat) {
    let filteredSubCategoryList = []
    let filteredItem = []
    if (cat != 'All') {
      if (this.filterKeys.ItemType != 'All') {
        filteredItem = this.curDataSource.filter(item => item['category'].toUpperCase() === cat.toUpperCase()
          && item['ItemType'].toUpperCase() === this.filterKeys.ItemType.toUpperCase());
      }
      else {
        filteredItem = this.curDataSource.filter(item => item['category'].toUpperCase() === cat.toUpperCase());
      }
      filteredItem.forEach(element => {
        filteredSubCategoryList.push(element.subCategory)
      });
      this.subCategoryList = filteredSubCategoryList.filter((k, i, ar) => ar.indexOf(k) === i).sort();
      this.subCategoryList.splice(0, 0, 'All')
    }
    else if (this.filterKeys.ItemType != 'All') {
      this.subCategoryList = this.subCatList.sort()
    }
    else {
      this.subCategoryList = this.initSubCategoryList
    }
    this.filterKeys.category = cat;
    this.filterKeys.subCategory = 'All'
    this.allFilter()
  }

  selectSubCat(subCat) {
    this.filterKeys.subCategory = subCat;
    this.allFilter()
  }

  adjustReq() {
    let itemsToAdjust = [];    
    if (this.indentArea == 'STORE') { 
    itemsToAdjust = this.inventoryItems.filter(item => item.adjustQty > 0 && item.hasOwnProperty('reason'))
    } else {
      itemsToAdjust = this.inventoryItems.filter(item => item.adjustQty > 0 && Object.keys(item.workArea).includes(this.indentArea) && item.hasOwnProperty('reason'))
    }
    this.raiseAdjustInvReq(itemsToAdjust)
  }

  raiseAdjustInvReq(itemsToAdjust) {
    if (itemsToAdjust.length > 0) {
      itemsToAdjust.forEach(item => {
        delete item.mId;
        delete item._id;
        item.spoilageStatus = 'pending'
    });
      this.branchTransfer.updateSpoilage({
        invItems: itemsToAdjust,
        userEmail: this.user.email,
        user: this.user.mId,
        tenantId: this.user.tenantId,
        indentArea: this.indentArea,
        restaurantId: this.restaurantId,
        documentDate: this.documentDate.value,
        status : 'pending'
      }).subscribe(data => {
        this.utils.openSnackBar('Ledger Record Created Successfully', null, 3000)
        this.getBranchInv();
      }, err => console.error(err))
    }
    else {
      this.utils.snackBarShowWarning('No Items to issue. Please add Item');
    }
  }

  clear() {
    this.category.setValue('')
    this.Subcategory.setValue('')
    this.ItemType.setValue('')
    this.searchText = ''
    this.dataSource.data = this.inventoryItems.filter(item => Object.keys(item.workArea).includes(this.indentArea))
    this.doFilter(this.searchText)
    this.getBranchInv();
  }

  preview() {
    this.showFooter = !this.showFooter;
    if (this.adjInvPreview == true) {
      this.curDataSource = this.dataSource.data
      this.dataSource.data = this.inventoryItems.filter(item => item.adjustQty > 0)
      // this.dataSource.data = this.inventoryItems.filter(item => item.adjustQty > 0 && Object.keys(item.workArea).includes(this.indentArea))
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
      this.dataSource.paginator = this.paginator;
    }
    else {
      this.dataSource.data = this.curDataSource
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
      this.dataSource.paginator = this.paginator;
    }
  }

  clearSearchText() {
    this.searchText = ''
    this.doFilter(this.searchText)
  }


  getTotalReturnCost(element) {
    this.reqSelected = (element && element.adjustQty > 0 && (!('reason' in element) || element.reason.length === 0)) ? false : true ;
    let sum = 0;

    
    this.utils.truncateNew(element.adjustQty)
    if (element != null) {
      // if (this.adjustInvFlag) {
      //   if (element.adjustType === 'Dec')
      //     element.adjustQty > element.inStock ? element.adjustQty = element.inStock : element.adjustQty;
      // }
      // else {
        // if (element.adjustType === 'Dec')
        //   element.adjustQty > element.workArea[this.indentArea] ? element.adjustQty = element.workArea[this.indentArea] : element.adjustQty;
      // }
    }

    this.dataSource.data.forEach(element => {
      if (element.adjustQty > 0 && element.withTaxPrice > 0) {
        sum = sum + element.withTaxPrice * element.adjustQty
      }
    });
    this.totalIndentCost = sum

    if(this.itemtpe){
      this.selectItemType(this.itemtpe);
    }
    if(this.cat){
      this.selectCategory(this.cat);
    }
    if(this.subCat){
      this.selectSubCat(this.subCat);
    }
  }

  downloadFile(data, filename = "data") {
    let csvData = this.ConvertToCSV(data, [
      "itemName",
      "inStock",
      "inKitchen",
      "uom",
    ]);
    let blob = new Blob(["\ufeff" + csvData], {
      type: "text/csv;charset=utf-8;",
    });
    let dwldLink = document.createElement("a");
    let url = URL.createObjectURL(blob);
    let isSafariBrowser =
      navigator.userAgent.indexOf("Safari") != -1 &&
      navigator.userAgent.indexOf("Chrome") == -1;
    if (isSafariBrowser) {
      dwldLink.setAttribute("target", "_blank");
    }
    dwldLink.setAttribute("href", url);
    dwldLink.setAttribute("download", filename + ".csv");
    dwldLink.style.visibility = "hidden";
    document.body.appendChild(dwldLink);
    dwldLink.click();
    document.body.removeChild(dwldLink);
  }

  ConvertToCSV(objArray, headerList) {
    let array = typeof objArray != "object" ? JSON.parse(objArray) : objArray;
    let str = "";
    let row = "S.No,";
    for (let index in headerList) {
      row += headerList[index] + ",";
    }
    row = row.slice(0, -1);
    str += row + "\r\n";
    for (let i = 0; i < array.length; i++) {
      let line = i + 1 + "";
      for (let index in headerList) {
        let head = headerList[index];

        line += "," + array[i][head];
      }
      str += line + "\r\n";
    }
    return str;
  }

  changeButtonText(element) {
    if (element.adjustType == 'Dec') {
      element.adjustType = 'Inc'
      element.icon = 'arrow_upward'
    }
    else {
      element.adjustType = 'Dec'
      element.icon = 'arrow_downward'
      this.getTotalReturnCost(element)
    }
  }

  permissionCall(event,element){
    let Data = this.inventoryItems.filter(item => item.adjustQty > 0), value ;
    Data = Data.filter(obj => (!('reason' in obj) || obj.reason.length === 0));
    if (event.length > 0 && element.adjustQty > 0 && Data.length == 0){
      this.reqSelected = true;
    } else {
      this.reqSelected = false;
    }
  }

  refreshdata(){
      this.selectWorkArea(this.selectedWorkArea);
  }
  
  focusFunctionWithOutForm(element , value){
    if(Number(element[value]) === 0){
      element[value] = null;
    }
  }
  
  focusOutFunctionWithOutForm(element , value){
    if(element[value] === null){
      element[value] = 0
    }
  }

}
