import { Component, OnInit, OnChanges, ViewChild, TemplateRef } from "@angular/core";
import { BranchTransferService, AuthService, MenuItemService, ShareDataService } from "../_services";
import { GlobalsService } from "../_services/globals.service";
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { Router } from "@angular/router";
import { MatTableDataSource, MatPaginator, MatSort, MatDialog } from "@angular/material";
import { UtilsService } from "../_utils/utils.service";
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { SelectionModel } from "@angular/cdk/collections";
import { InventoryService } from "../_services/inventory.service";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { environment } from "../../environments/environment";
import { NotificationService } from '../_services/notification.service'
import { MasterdataupdateService } from "../_services/masterdataupdate.service";
import { interval, Subscription,ReplaySubject, Subject} from 'rxjs';
import {takeUntil } from 'rxjs/operators';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import { ChangeDetectorRef } from '@angular/core';
import * as jsonData from '../../assets/config.json';
import * as XLSX from 'xlsx';
export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};
@Component({
  selector: 'app-closing',
  templateUrl: './closing.component.html',
  styleUrls: ['./closing.component.scss', "./../../common-dark.scss"],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
            { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
          ]
})
export class ClosingComponent implements OnInit {
  @ViewChild('fileInput') fileInput: any;
  public toggleToWorkArea = false;
  public user: any;
  public displayMessage: any;
  public showCatList: boolean = true;
  public inventoryItems: any[];
  public workAreaItemsList: any[];
  public displayedColumns;
  public jobsDisplayedColumns;
  public openPkgUom: any;
  public selectedCatVal: any;
  weightToCapacity;
  curItem: any = {};
  dataSource: MatTableDataSource<any>;
  // jobsDataSource: MatTableDataSource<any>;
  jobsDataSource = new MatTableDataSource();
  @ViewChild(MatSort) sort: MatSort;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  categories: any[];
  all = "ALL";
  isStore = false;
  displayPkgFlag: boolean = false;
  packageItems: any[];
  freeQtyDesc: string;
  displayFreePackage: boolean;
  entryAccess: boolean = true;
  openDialouge: boolean = true;
  closingCompleted: boolean = false;
  overallProgress: any = 0;
  sameUserSubcat: boolean = true
  selectedSubCatVal: any
  kitchenClosingUrl : string;
  closingStockUrl : string;
  transferClosingUrl = encodeURI(GlobalsService.transferClosing);
  adjustInventoryUrl = encodeURI(GlobalsService.adjustInventory);
  category = new FormControl("", [Validators.required]);
  itemTypeForm = new FormControl("", [Validators.required]);
  subCategory = new FormControl("", [Validators.required]);
  public vendorFilterCtrl: FormControl = new FormControl();
  public vendorsBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  public VendorBank: any[] = [];
  itemTypes = ["All", "Inventory", "Menu Items", "Sub Receipe"];
  ItemType = ["All"];
  category_new = [];
  categoryStruct = [];
  subCategories = [];
  invCategories = [];
  menuGroups = [];
  allCategories = [];
  selection = new SelectionModel<any>(true, []);
  currDate;
  reasons: any[];
  init_category_new: any[];
  init_subCategories: any[];
  title = "Closing Stock";
  btnTxt = "Save Inventory";
  finalSubmit = false
  selectedItemType = "ALL";
  selectedCategory = "ALL";
  selectedSubCategory = "ALL";
  indentAreas: any[] = ["No Selection"];
  indentArea: any;
  catCount: any = {};
  savedItems: any = {};
  savedClosingInfo: any = {}
  showSubCat: boolean = false;
  showItems: boolean = false;
  transferClosing: boolean = false;
  emailAsKey: any;
  restaurantId: any;
  closingType: any;
  date: any;
  searchText: any;
  curItemIndex: any;
  filteredWorkAreasList: any;
  finalMsg: any;
  missItemList: any = [];
  multiBranchUser; branchSelected: boolean;
  @ViewChild("table") private _table: any;
  pageSizes: any[];
  public closingDate = new FormControl();
  public fileObj: any;
  public isValidFormat: boolean = true;
  public fileContent: any = '';
  public baseUrl: string;
  selectEvent: any;
  jobs: FormGroup;
  closingForm: FormGroup;
  events = [
    { id: 1, value: 'excelStockClosing', displayValue: 'Uploaded Templates', },
    { id: 2, value: 'template', displayValue: 'Generated Templates', },
  ];
  currentBranchName: any;
  currentWorkAreaName: any;
  interval: Subscription;
  loading: boolean;
  branches: any[];
  getBranchData: any[]
  latestDownloadedTemplateNumber: any;
  latestDownloadedTemplateType: any;
  private specialKeys: Array<string> = ['Backspace', 'Tab', 'End', 'Home', 'ArrowLeft', 'ArrowRight', 'Del', 'Delete'];
  private regex: RegExp = new RegExp(/^\d*\.?\d{0,2}$/g);
  private unsubscribe$ = new Subject<void>();
  errorData: any[] = [];
  newArray: any;
  tempSheetNames: any[] = [];
  allowToUpload: boolean = false;
  navigateData: string;

  excelfile: File;
  headers: string[];
  @ViewChild('openExcelError') openExcelError: TemplateRef<any>;
  hoveredData : any;
  showTooltip: boolean = false;
  hoveredElement: any = null;
  selectedOption : boolean = false;
  constructor(
    private cdRef:ChangeDetectorRef,
    private fb: FormBuilder,
    private masterDataService: MasterdataupdateService,
    private notifyService: NotificationService,
    private menuItems: MenuItemService,
    private http: HttpClient,
    private auth: AuthService,
    private branchTransfer: BranchTransferService,
    private dialog: MatDialog,
    private utils: UtilsService,
    private inventory: InventoryService,
    public router: Router,
    private sharedData: ShareDataService
  ) {

    var windowLocation = window.location.href;
    let windowCurrentUrl = windowLocation.split('/')[4]
    
    if(windowCurrentUrl == encodeURI(GlobalsService.closingStock)){
      this.closingStockUrl = encodeURI(GlobalsService.closingStock) 
    }else if(windowCurrentUrl == encodeURI(GlobalsService.storeclosing)){
      this.closingStockUrl = encodeURI(GlobalsService.storeclosing);
    }

    if(windowCurrentUrl == encodeURI(GlobalsService.stockClosure)){
      this.kitchenClosingUrl = encodeURI(GlobalsService.stockClosure) 
    }else if(windowCurrentUrl == encodeURI(GlobalsService.workareaClosing)){
      this.kitchenClosingUrl = encodeURI(GlobalsService.workareaClosing);
    }

    this.user = this.auth.getCurrentUser();
    this.multiBranchUser = this.user.multiBranchUser
    if (this.user.restaurantAccess == 1) {
      this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld
    }
    this.reasons = GlobalsService.adjustInventoryReasons.sort();
    this.baseUrl = environment.baseUrl;

    this.jobs = this.fb.group({
      jobs: [null, Validators.required],
      sourceType: ['Excel'],
    });

    this.closingForm = this.fb.group({
      branchSelection: [null, Validators.required],
      workAreaSelection: [null, Validators.required]
    });

      if(this.multiBranchUser){
      this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
        this.getBranchData = val;
        if(this.getBranchData.length == 0 ){
            this.branches = this.user.restaurantAccess;
          }else if(this.getBranchData.length == 1){        
            const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
            this.closingForm.get('branchSelection').setValue(toSelect);
            this.branches = this.getBranchData
            if (this.router.url.includes(this.closingStockUrl) || this.router.url.includes(this.transferClosingUrl)) {
              this.transferClosing = true;
              this.jobs.get('jobs').setValue(this.events.find((c) => c.id == 1));
            }
            this.filterByBranch(this.closingForm.value.branchSelection);
          }else{
            this.branches = this.getBranchData
          }
      });
      }
  }
  todayDate: Date = new Date();
  yesterday: Date = new Date();

  ngAfterViewChecked(){
    this.cdRef.detectChanges();
  }
  
  ngOnInit() {
    if (this.router.url.includes(this.transferClosingUrl) || this.router.url.includes(this.closingStockUrl)) {
      this.transferClosing = true
    }
    this.yesterday.setDate(this.yesterday.getDate() - 31);
    this.jobs.get('jobs').setValue(this.events.find((c) => c.id == 1));
    this.date = new Date();
    this.currDate = new Date();

    this.emailAsKey = this.user.email.replace(/\./g, '#');
    if (this.router.url.includes(this.kitchenClosingUrl)) {
      this.closingType = 'kitchenClosing'
    }
    else if (this.router.url.includes(this.closingStockUrl) || this.router.url.includes(this.transferClosingUrl)) {
      if (!this.toggleToWorkArea) {
        this.closingType = 'storeClosing'
      } else {
        this.closingType = 'kitchenClosing'
      }
    }
    if (!this.user.multiBranchUser) {
      this.branches = this.user.restaurantAccess;
      this.entryAccess = true;
        this.currentBranchName = this.user.restaurantAccess[0];    
      if (this.user.restaurantAccess[0].workAreas.length == 1) {
        this.currentWorkAreaName = this.user.restaurantAccess[0].workAreas[0];
        this.category.setValue("");
        this.subCategory.setValue("");
        this.itemTypeForm.setValue("");
        this.indentArea = this.user.restaurantAccess[0].workAreas[0];
        this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld;
        this.getJobs();
        this.newFunction();

        this.user.restaurantAccess.forEach(element => {
          if (element.restaurantIdOld == this.restaurantId) {
            this.filteredWorkAreasList = element.workAreas;
          }
        });
        this.VendorBank = this.filteredWorkAreasList ;
        this.vendorsBanks.next(this.VendorBank.slice());
        this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.vendorfilterBanks();
        });
        this.closingForm.get('workAreaSelection').setValue(this.currentWorkAreaName);
      }
      else if (this.user.restaurantAccess[0].workAreas.length > 1) {
        this.filterByBranch(this.user.restaurantAccess[0]);
      }
    } 
  }

  getErrorLog(eleObj:any) {
    let obj = this.user;
    obj["masterDataUpdateId"]=  eleObj.details.templateNo
    this.masterDataService.getErrorLog(obj).subscribe((response: any) => {
      if (response.success) {
        // var downloadLink = document.createElement("a");
        // downloadLink.href = 'data:application/txt;base64,' + response.eFile;
        // downloadLink.download = "error_log_"+eleObj.details.templateNo+".txt";
        // document.body.appendChild(downloadLink);
        // downloadLink.click();
        // document.body.removeChild(downloadLink);
        var textData = atob(response.eFile); // Decode the Base64-encoded data to text
        const newWindow = window.open('', '_blank');
        if (newWindow) {
          newWindow.document.open();
          newWindow.document.write('<pre>' + textData + '</pre>');
          newWindow.document.close();
        }
      }else{
        this.utils.snackBarShowWarning(response.message);
      }
    });
  }  


  openDialougeBox(){
  this.dialog.open(SimpleDialogComponent, {
    data: {
      // title: 'Closing Alert',
      // msg: `Are you sure you want to do upload template for ${this.restaurantId} : ${this.indentArea}?`,
      ok: function () {
        document.getElementById("getFile").click()
      }.bind(this)
    }
  });
}

  portionChange() {    
    if (this.selectedOption) {
        this.dataSource.data = this.dataSource.data.map(element => {
            if (element.ItemType === 'SubRecipe' && element.hasOwnProperty('portionWeight')) {
                element['uom'] = 'portion';
                element.selectedOption = 'portion';
                let conversionCoefficient =  element['defaultUOM'] == 'NOS' ? 1: 1000 ;
                element['inStock'] = element['currentStock'] / (element.portionWeight / conversionCoefficient)
                element['price'] = element['actualPrice'] / (element.price / conversionCoefficient)
            }
            return element;
        });
    } else {
        this.dataSource.data = this.dataSource.data.map(element => {
            if (element.ItemType === 'SubRecipe' && element.hasOwnProperty('portionWeight')) {
                element['uom'] = 'uom'; 
                element.selectedOption = 'uom';
                element['uom'] = element['defaultUOM']
                element['inStock'] = element['currentStock']
                element['price'] = element['actualPrice']
            }
            return element;
        });
    }
  }

  focusWithOutForm(curItem , value){
    if(Number(curItem[value]) === 0){
      curItem[value] = null;
    }
  }
  
  focusOutWithOutForm(curItem , value){
    if(curItem[value] === null){
      curItem[value] = 0
    }
  }


refreshtable(){
  // if(this.jobs.value.sourceType === 'UI'){
  //   this.filterJobsData()
  // }else{
    this.getJobs();
  // }
}

  getJobs() {
    let obj = {};
    obj["tenantId"] = this.user.tenantId;
    obj["restaurantId"] = this.restaurantId;
    obj['event'] = this.jobs.value.jobs.value;
    obj['workArea'] = this.indentArea;
    this.masterDataService.getJobs(obj).subscribe((response: any) => {
      if (response.success) {
        this.jobsDataSource.data = response.data;
        if (obj['event'] === 'excelStockClosing'){
          this.jobsDisplayedColumns = ['templateId', 'email', 'type', 'indentArea','createTs','closingDate', 'download','status', 'error'];
        } else {
          this.jobsDisplayedColumns = ['templateId', 'email', 'type', 'indentArea', 'generateTs','download','status', 'error'];
        }
        this.jobsDataSource.paginator = this.paginator;
      }
    });
  }

  filterJobsData() {
    let obj = {}
    obj['tenantId'] = this.user.tenantId
    obj['restaurantId'] = this.closingForm.value.branchSelection.restaurantIdOld
    obj['workArea'] = this.closingForm.value.workAreaSelection
    obj['event'] = 'report';
    obj['type'] = 'systemClosingReport';
    this.masterDataService.closingReports(obj).subscribe((res: any) => {
      if (res['success'] == true) {
        if(this.jobs.value.sourceType === 'UI'){
          this.jobsDisplayedColumns = ['templateId', 'email', 'type', 'indentArea','createTs','closingDate','status', 'error'];
        }
        this.jobsDataSource.data = res['data'];
        this.jobsDataSource.paginator = this.paginator;
      }
    }, err => {
      console.log(err)
    });
  }

  download(inputs) {
    let obj = {}
    obj['tenantId'] = this.user.tenantId;
    obj['reportName'] = "systemClosingReport";
    this.menuItems.getFileFormat(obj).subscribe(res => {
      if (res['result'] == 'success') {
        let fileFormat = res['data']
        let user = this.user;
        const date = new Date(inputs.createTs);
        const formatter = new Intl.DateTimeFormat('en-IN', {
            timeZone: 'Asia/Kolkata',
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        });
        const formattedDate = formatter.format(date);
        const [datePart, timePart] = formattedDate.split(', ');
        const [day, month, year] = datePart.split('/');
        const [time, period] = timePart.split(' ');
        const [hours, minutes] = time.split(':');
        const formattedCreatedAt = `${day}-${month}-${year}|${hours}:${minutes}_${period.toUpperCase()}`;        
        var fullName = `${inputs.reportNo}_systemClosingReport_${formattedCreatedAt}.${fileFormat}`

        var counter = 0;
        this.interval= interval(3000).subscribe((_x =>{
          this.retrieveReport(fullName, user);
          counter += 1;
          if (counter > 24) this.interval.unsubscribe();
        }));
      }
    });
  }

  retrieveReport(this, reportName, user) {
    let obj = user;
    obj['type'] = reportName;
    this.menuItems.retrieveReport(obj).subscribe(res => {
      if (res['result'] == 'success') {
        var downloadLink = document.createElement("a");
        downloadLink.href = 'data:application/vnd.ms-excel;base64,' + res.eFile;
        let firstCode = reportName.split("_")[0]
        let latName = reportName.split(".")[1];
        let alteredReportName = firstCode + '_' + 'System Closing Report' + '.' + latName
        downloadLink.download = alteredReportName;
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
        this.interval.unsubscribe();
        this.utils.snackBarShowSuccess('File downloaded successfully');
      }
    }, err => {
      console.log(
        err
      )
    });
  }

  downloadTemplates(element){
    if(this.jobs.value.jobs.value == 'template'){
      let obj = {};
      obj["tenantId"] = this.user.tenantId;
      obj["restaurantId"] = this.restaurantId;
      obj["email"] = this.user.email;
      obj["templateNo"] = element.templateNo;
      obj['event'] = this.jobs.value.jobs.value;
      obj['indentArea'] = this.indentArea;
      obj['type'] = element.details.type;
      obj['fullName'] = obj["templateNo"] +  "_" + this.closingType + '.xlsx'
      this.masterDataService.downloadTemplates(obj).subscribe((res: any) => {
        // this.menuItems.retrieveTemplate(obj).subscribe((res: any) => {
        if (res['result'] == 'success') {
          var downloadLink = document.createElement("a");
          downloadLink.href = 'data:application/vnd.ms-excel;base64,' + res.eFile;
          downloadLink.download = res.fileName;
          document.body.appendChild(downloadLink);
          downloadLink.click();
          document.body.removeChild(downloadLink);
        } else {
          if("message" in res){
            this.utils.snackBarShowError(res['message']);
          }else{
            this.utils.snackBarShowError('Something went wrong,please try again');
          }
        }
      });
      
    }else{
      let obj = {};
      obj["tenantId"] = this.user.tenantId;
      obj["restaurantId"] = this.restaurantId;
      obj["email"] = this.user.email;
      obj["templateNo"] = element.details.templateNo;
      obj['event'] = this.jobs.value.jobs.value;
      obj['indentArea'] = this.indentArea;
      obj['type'] = element.details.closingType;
      obj['fullName'] = obj["templateNo"] +  "_" + this.closingType + '.xlsx'
      this.masterDataService.downloadTemplates(obj).subscribe((res: any) => {
        // this.menuItems.retrieveTemplate(obj).subscribe((res: any) => {
        if (res['result'] == 'success') {
          var downloadLink = document.createElement("a");
          downloadLink.href = 'data:application/vnd.ms-excel;base64,' + res.eFile;
          downloadLink.download = res.fileName;
          document.body.appendChild(downloadLink);
          downloadLink.click();
          document.body.removeChild(downloadLink);
        } else {
          this.utils.snackBarShowError("Something went Wrong");
        }
      });
    }
  }

  // downloadClosing(element) {
  //   let obj = {}
  //   obj['tenantId'] = this.user.tenantId;
  //   obj['restaurantId'] = this.user.restaurantId;
  //   obj['templateNo'] = element.templateNo;
  //   obj['type'] = element.details.type;
  //   // obj['selectedBranch'] = element.selectedBranch;
  //   obj['fullName'] = obj["templateNo"] +  "_" + obj['type'] + '.xlsx'
  //   this.masterDataService.downloadClosing(obj).subscribe(res => {
  //     if (res['success'] == true) {
  //       this.utils.showSuccess("successfully", "Success")
  //       var downloadLink = document.createElement("a");
  //       downloadLink.href = 'data:application/vnd.ms-excel;base64,' + res.eFile;
  //       downloadLink.download = res.fileName;
  //       document.body.appendChild(downloadLink);
  //       downloadLink.click();
  //       document.body.removeChild(downloadLink);
  //     } else {
  //       this.utils.showError("Something went Wrong", "error")
  //     }
  //   });
  // }

  applyFilter() {
    this.getJobs();
  }

  checkUpload() {
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Closing Alert',
        // msg: `Are you sure you want to do upload template for ${this.restaurantId} : ${this.indentArea}?`,
        msg: `Are you sure to upload template for  ${ this.restaurantId} : ${this.indentArea}?`,
        ok: function () {
          document.getElementById("getFile").click()
        }.bind(this)
      }
    });
  }

  uploadTemplate() {
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Template Upload Alert',
        msg: `Are you sure to upload template for ${this.restaurantId} : ${this.indentArea}?`,
        ok: function () {
          document.getElementById("getTemplateFile").click()
        }.bind(this)
      }
    });
  }

  checkClosingDate() {
    if (this.closingDate.value) {
      return true
    } else {
      this.utils.snackBarShowWarning("Please select closing date");
      return false
    }
  }

  checkActiveUser() {
    let obj = {}
    obj["tenantId"] = this.user.tenantId
    obj["indentArea"] = this.indentArea
    obj["restaurantId"] = this.restaurantId
    if (this.router.url.includes(this.closingStockUrl) || this.router.url.includes(this.transferClosingUrl)) {
      if (!this.toggleToWorkArea) {
        obj['isStoreClosing'] = true;
      } else {
        obj['isKitchenClosing'] = true;
      }
    }
    else if (this.router.url.includes(this.kitchenClosingUrl)) {
      obj['isKitchenClosing'] = true
    }
    this.inventory.getActiveUser(obj).subscribe((data) => {
      if (data != null) {
        if (data["activeUser"]) {
          var email = Object.keys(data["users"])
          var message = "Active Users::\n"
          email.forEach(element => {
            var workArea = Object.keys(data["users"][element]);
            var index = element.indexOf('@');
            element = element.substring(0, index != -1 ? index : element.length);
            var temp = `${element} - ${workArea}\n`;
            message = message + temp
          });
          this.showToasterInfo(message);
        } else {
          this.showToasterInfo(data["message"]);
        }
      }
      else {
        this.showToasterError('Ah,there is some issue\nPlease check with CRM team');
      }
    });
  }

  getFileFormatForClosing(event: any): void{
    let obj = {}
    obj["tenantId"] = this.user.tenantId
    obj["indentArea"] = this.indentArea
    obj["restaurantId"] = this.restaurantId
    this.masterDataService.getFileFormatForClosing(obj).subscribe((res: any) => {
      if (res['success'] == true) {
        this.latestDownloadedTemplateNumber = res['data'][0].templateNo
        this.latestDownloadedTemplateType = res['data'][0].type
        // const file = $event.target.files[0];
        this.fileChange(event)
      } else {
        this.utils.snackBarShowError(res['message']);
      }
    });
  }

  fileChange(event) {
    if (this.checkClosingDate()) {
      this.excelfile = event.target.files[0];
      this.extractHeaders(event);
    }
  }

  setExcelFile(event){
      let files = event.target.files;
      let file = files[0];
      this.fileObj = file;
      this.isValidFormat = true;
      var reader = new FileReader();
      reader.onloadend = (e) => {
        this.fileContent = reader.result;
        this.uploadFile(this.fileContent, this.fileObj.name, this.fileObj.type);
      };
      reader.readAsDataURL(file);
  }

  handleTemplateUpload(event: any): void {
    if (this.checkClosingDate()) {
      const file = event.target.files[0];
      if (!file) {
        this.utils.snackBarShowWarning("Please select a file");
        return;
      }

      // Validate file format
      const allowedFormats = ['xlsx', 'xls'];
      const fileExtension = file.name.split('.').pop()?.toLowerCase();

      if (!allowedFormats.includes(fileExtension)) {
        this.utils.snackBarShowWarning("Please upload Excel format only (.xlsx or .xls)");
        return;
      }

      this.processTemplateFile(file);
    }
  }

  processTemplateFile(file: File): void {
    const fileReader = new FileReader();

    fileReader.onload = (e) => {
      try {
        const data = new Uint8Array(fileReader.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });

        // Convert all sheets to JSON
        const jsonData: any = {};

        workbook.SheetNames.forEach(sheetName => {
          const worksheet = workbook.Sheets[sheetName];
          const sheetData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

          // Convert to proper JSON format with headers
          if (sheetData.length > 0) {
            const headers = sheetData[0] as string[];
            const rows = sheetData.slice(1);

            jsonData[sheetName] = rows.map(row => {
              const rowObj: any = {};
              headers.forEach((header, index) => {
                rowObj[header] = row[index] || '';
              });
              return rowObj;
            });
          }
        });

        console.log('Template JSON Data:', jsonData);
        this.utils.snackBarShowSuccess(`Template processed successfully! Found ${Object.keys(jsonData).length} sheet(s)`);

        // You can now use jsonData for further processing
        this.processTemplateData(jsonData);

      } catch (error) {
        console.error('Error processing template file:', error);
        this.utils.snackBarShowError('Error processing template file. Please check the file format.');
      }
    };

    fileReader.readAsArrayBuffer(file);
  }

  processTemplateData(jsonData: any): void {
    // Process the converted JSON data here
    // This method can be customized based on your specific requirements
    console.log('Processing template data:', jsonData);

    // Example: You can send this data to a service or process it further
    // this.masterDataService.processTemplateData(jsonData).subscribe(response => {
    //   // Handle response
    // });
  }

  // fileChange(event) {
    // if (this.checkClosingDate()) {
    //   let files = event.target.files;
    //   let file = files[0];
    //   this.fileObj = file;
    //   let possibleFormat = ['XLSX', 'XLS']
    //   let fileExtension = this.fileObj.name.split(".")[1];
    //   if (!possibleFormat.includes(fileExtension.toUpperCase())) {
    //     this.isValidFormat = false;
    //     this.utils.snackBarShowWarning("Please upload excel format only");
    //   } else {
    //     this.isValidFormat = true;
    //     var reader = new FileReader();
    //     reader.onloadend = (e) => {
    //       this.fileContent = reader.result;
    //       this.uploadFile(this.fileContent, this.fileObj.name, this.fileObj.type);
    //     };
    //     reader.readAsDataURL(file);
    //   }
    // }
  // }

  extractHeaders(event) {
    let files = event.target.files;
    let file = files[0];
    this.fileObj = file;
    let check :any;
    var validationResult: any;
    const jsonDataError: any[] = [];
    const fileReader = new FileReader();
    fileReader.onload = (e) => {
      const data = new Uint8Array(fileReader.result as ArrayBuffer);
      const workbook = XLSX.read(data, { type: 'array' });
      const headers = [];
      
      workbook.SheetNames.forEach(sheetName => {
        const worksheet = workbook.Sheets[sheetName];
        const sheetHeaders = Object.keys(worksheet).filter(key => /^[A-Z]+1$/.test(key)).map(key => worksheet[key].v);
        headers.push(sheetHeaders);
        const expectedColumns  = jsonData['default'].closing
        validationResult = this.validateSheetHeader(sheetName, headers[0], expectedColumns, check);
        this.errorData.push(validationResult);
        this.tempSheetNames.forEach(sheetName => {
          const foundObject = this.errorData.find(obj => obj.sheetName === sheetName);
          if (!foundObject) {
            this.errorData.push({ "sheetName": sheetName, "missingColumns": [], "check": true });
          }
        });

        const hasFalseCheck = this.errorData.some(item => item.check === false);
        this.allowToUpload = hasFalseCheck ? false : true;
        if (validationResult.missingColumns.length > 0) {
          this.openDialog(validationResult);
          this.utils.snackBarShowError('Please add the missing columns and re-upload the sheet');
        } else {
          this.setExcelFile(event)
        }
      });
    };
    fileReader.readAsArrayBuffer(this.excelfile);
  }

  validateSheetHeader(sheetName: string, headerRow: any, expectedColumns: string[], check: boolean): { sheetName: string, missingColumns: string[], check: boolean } {
    if (!headerRow) {
      return { sheetName, missingColumns: [], check: true };
    }
    headerRow = headerRow.filter(value => value !== null && value !== undefined);
    const missingColumns = expectedColumns.filter(item => !headerRow.includes(item));
    check = missingColumns.length === 0 ? true : false;
    return { sheetName, missingColumns, check };
  }

  openDialog(validationResult): void {
    const dialogRef = this.dialog.open(this.openExcelError, { 
      maxWidth: '50vw',
      autoFocus: false,
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe(result => {
      this.errorData = []
    });
  }

  closeDialog(): void {
    this.dialog.closeAll();
  }

  uploadFile(file, name, type) {
    let obj = this.user;
    let inputObj = {
      fileObj: file,
      // name: name,
      name : this.latestDownloadedTemplateNumber + '_' + this.latestDownloadedTemplateType + ".xlsx",
      type: type,
      tenantId: this.user.tenantId,
      indentArea: this.indentArea,
      restaurantId: this.restaurantId,
      token: obj["token"],
      email: obj["email"],
      closingType: this.closingType,
      date: this.utils.dateCorrection(this.closingDate.value)
    }
    let headers = new HttpHeaders({
    });

    let options = {
      headers: headers,
      body: obj
    }
    this.http.post(this.baseUrl + 'uploadClosingData', inputObj, options).subscribe((response: any) => {
      if (response.success) {
        this.dialog.open(SimpleDialogComponent, {
          data: {
            title: 'Closing Alert',
            msg: response['message'],
            ok: function () {
            }.bind(this)
          }
        });
      }
    });
  }

  ngOnDestroy() {
    if (this.interval) {
      this.interval.unsubscribe();
    }
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  generateTemplate() {
    let obj = this.user;
    obj['type'] = this.closingType;
    obj['indentArea'] = this.indentArea;
    obj['restaurantId'] = this.restaurantId;
    obj['isStore'] = this.isStore
    this.dialog.closeAll()
    this.menuItems.generateTemplateJob(obj).subscribe(res => {
      if (res['result'] == 'success') {
        this.utils.snackBarShowSuccess(res['message'] + ':' + res['templateNo']);

        var counter = 0;
        this.interval= interval(3000).subscribe((_x =>{
          let obj = this.user;
          obj['type'] = this.closingType;
          obj['indentArea'] = this.indentArea;
          obj['restaurantId'] = this.restaurantId;
          obj['fullName'] = res['templateNo'] + '_' + this.closingType + '.xlsx'
          this.latestDownloadedTemplateNumber = res['templateNo']
          this.menuItems.retrieveTemplate(obj).subscribe(res => {
            if (res['result'] == 'success') {
              var downloadLink = document.createElement("a");
              downloadLink.href = 'data:application/vnd.ms-excel;base64,' + res.eFile;
              downloadLink.download = res.fileName;
              document.body.appendChild(downloadLink);
              downloadLink.click();
              document.body.removeChild(downloadLink);
              this.interval.unsubscribe();
            }
          }, err => {console.log(err )
          });
          counter += 1;
          if (counter > 24) this.interval.unsubscribe();
        }));
      }
      else {
        this.utils.snackBarShowError("Something went wrong, please try again!");
      }
    }, err => {
      console.log(
        err
      )
    });
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  };

  selectCategory(val, i) {
    this.showSubCat = true
    this.selectedCatVal = val
    this.selectedCategory = val.toUpperCase();
    this.selection.clear();
    this.subCategories = this.categoryStruct[val].subCategory
    this.universalFilter();
    this.selectActiveLink("cat", i);
  }

  fetchClosingData(val) {
    this.inventory.fetchClosingData({
      tenantId: this.user.tenantId,
      indentArea: this.indentArea,
      restId: this.restaurantId,
      type: this.closingType,
    }).subscribe((data) => {
      if (data != null) {
        this.savedClosingInfo = data
        this.categoryStruct = this.savedClosingInfo.workAreas[this.indentArea].category
        this.subCategories = this.categoryStruct[this.selectedCatVal].subCategory
        if (this.savedClosingInfo.workAreas[this.indentArea].category[this.selectedCatVal].subCategory[val].hasOwnProperty('items')) {
          this.savedItems = this.savedClosingInfo.workAreas[this.indentArea].category[this.selectedCatVal].subCategory[val]['items']
        }
        else {
          this.savedItems = {}
        }
      }
      this.processLocking(val)
    });
  }

  processLocking(val) {
    if (this.savedClosingInfo.hasOwnProperty('users')) {
      if (this.subCategories[val].user == this.emailAsKey) {
        this.subCategories[val].underProcess = false
      }
      if (!this.subCategories[val].underProcess) {
        this.showItems = true;
        this.selectedSubCategory = val.toUpperCase();
        this.selection.clear();
        this.universalFilter();
        this.inventory.saveSubCat({
          tenantId: this.user.tenantId,
          restId: this.restaurantId,
          email: this.user.email.replace(/\./g, '#'),
          userName: this.user.name,
          subCat: val,
          indentArea: this.indentArea,
          cat: this.selectedCatVal,
          closingData: this.savedClosingInfo,
          type: this.closingType,
          userObj: this.savedClosingInfo.users[this.emailAsKey]
        }).subscribe((data) => {
        });
      }

    }
    else {
      this.showItems = true;
      this.selectedSubCategory = val.toUpperCase();
      this.selection.clear();
      this.universalFilter();
      let userObj = {}
      userObj[this.user.email.replace(/\./g, '#')] = { [this.indentArea]: { [this.selectedCatVal]: [val] } }
      this.inventory.saveUserSubCat({
        tenantId: this.user.tenantId,
        restId: this.restaurantId,
        email: this.user.email.replace(/\./g, '#'),
        subCat: val,
        cat: this.selectedCatVal,
        indentArea: this.indentArea,
        data: this.savedClosingInfo,
        userName: this.user.name,
        userObj: userObj,
        type: this.closingType
      }).subscribe((data) => {
      });
    }
  }

  selectSubCategory(val) {
    this.selectedSubCatVal = val
    this.fetchClosingData(val)
  }

  displayPackages(data, i) {     
  this.hoveredData = data;    
  if(! (typeof data === 'undefined')) {
    this.curItemIndex = i
    this.displayPkgFlag = true;
    if (this.savedClosingInfo.workAreas[this.indentArea].category[this.selectedCatVal].subCategory[this.selectedSubCatVal].hasOwnProperty('items')) {

      if(data.hasOwnProperty('itemCode')){
        if (this.savedClosingInfo.workAreas[this.indentArea].category[this.selectedCatVal].subCategory[this.selectedSubCatVal].items.hasOwnProperty(data.itemCode)) {
          if (data.packagingSizes.length > 0) {
            data.packagingSizes = this.savedClosingInfo.workAreas[this.indentArea].category[this.selectedCatVal].subCategory[this.selectedSubCatVal].items[data.itemCode].packagingSizes
          }
          else {
            let currentItem = this.savedClosingInfo.workAreas[this.indentArea].category[this.selectedCatVal].subCategory[this.selectedSubCatVal].items[data.itemCode]
            if (currentItem.hasOwnProperty('selectedOption') && currentItem['selectedOption'] == 'portion') {
              let conversionCoefficient = currentItem['uom'] === 'NOS' ? 1 : 1000;
              data.closingStock = this.utils.truncateNew((currentItem['closingStock'] * conversionCoefficient) / currentItem['portionWeight'])
              data['convertedWeight'] = currentItem['closingStock']
              data['uom'] = 'portion'
            } else {
              data.closingStock = this.savedClosingInfo.workAreas[this.indentArea].category[this.selectedCatVal].subCategory[this.selectedSubCatVal].items[data.itemCode].closingStock
            }
        }
        }
      }
    }
    this.packageItems = data.packagingSizes;
    if (data.displayFreePackage) {
      this.openPkgUom = data.closingUom.split("/")[1].toUpperCase();
    }

    this.packageItems.forEach((element) => {
      if (!element.otherPackages) element.otherPackages = 0;
      if (!element.orderedPackages) element.orderedPackages = 0;
      if (!element.numOfOpenPkg) element.numOfOpenPkg = 0;
    });
    if (!data.closingStock) data.closingStock = 0;
    this.displayFreePackage = data.displayFreePackage;
    if (!data.weightConvertedCpt) data.weightConvertedCpt = 0;
    data.weightToCapacity = this.weightToCapacity;
    this.curItem = data;
    this.selectActiveLink("item", i);
  }
}

  ckClosing() {
    if (this.closingType == 'storeClosing' || this.closingType == 'kitchenClosing') {
      this.dialog.open(SimpleDialogComponent, {
        data: {
          title: 'Special Closing Alert',
          msg: 'Are you sure you want to do this?',
          ok: function () {
            let obj = {
              tenantId: this.user.tenantId,
              indentArea: this.indentArea,
              restId: this.restaurantId,
              email: this.user.email.replace(/\./g, '#'),
              uId: this.user.mId,
              type: this.closingType,
              specialClosing: true
            }
            if (this.closingType == 'kitchenClosing') {
              obj["isInterBranchTransfer"] = true;
              obj["receiverWorkArea"] = this.indentArea;
            }
            this.inventory
              .updateItemsClosing(obj)
              .subscribe((data) => {
                if (data != null) {
                  this.utils.openSnackBar(data.msg, null, 3000);
                  this.finalSubmit = true
                  this.finalMsg = 'Closing has been completed successfully.Want to do it again? '
                }
              },
                (err) => {
                  console.error(err);
                });
          }.bind(this)
        }
      });
    }
    else {
      this.utils.snackBarShowWarning('Special closing can only be done for store');
    }
  }

  setClosingStock() {
    if (this.closingDate.value) {
      this.inventory.updateItemsClosing({
          tenantId: this.user.tenantId,
          restId: this.restaurantId,
          indentArea: this.indentArea,
          email: this.user.email.replace(/\./g, '#'),
          uId: this.user.mId,
          type: this.closingType,
          date: this.utils.dateCorrection(this.closingDate.value)
        })
        .subscribe((data) => {
          if (data != null) {
            if (data.hasOwnProperty('missItems')) {
              this.finalMsg = 'Closing has been completed successfully except these few items please ask digitory team to check.Want to do it again?'
              Object.values(data['missItems']).forEach(element => {
                this.missItemList.push(element)
              });
            }
            else {
              this.finalMsg = 'Closing has been completed successfully.Want to do it again? '
            }
            this.utils.openSnackBar(data.desc, null, 3000);
            this.finalSubmit = true
            this.filterJobsData();
            // this.generateReport()
          }
        },
          (err) => {
            console.error(err);
          });
    }
    else {
      this.utils.snackBarShowWarning('Please select closing date.');
    }
  }

  generateReport(){
    this.categoryStruct
    const categories = Object.keys(this.categoryStruct);
    const allSubCategoryKeys = [];
    for (const categoryKey in this.categoryStruct) {
      if (this.categoryStruct.hasOwnProperty(categoryKey)) {
          const category = this.categoryStruct[categoryKey];
          if (category.hasOwnProperty('subCategory')) {
              const subCategory = category['subCategory'];
              const subCategoryKeys = Object.keys(subCategory);
              allSubCategoryKeys.push(...subCategoryKeys);
          }
      }
  }
  
  let obj = {}
    obj = this.user;
    obj['type'] = "systemClosingReport";
    obj['requestDate'] = this.closingDate.value;
    obj['createTs'] = new Date();
    obj['selectedRestaurants'] = [this.closingForm.value.branchSelection.restaurantIdOld];
    obj['categories'] = categories
    obj['subCategories'] = allSubCategoryKeys;
    obj['selectedWorkAreas'] = this.closingForm.value.workAreaSelection;
    this.menuItems.requestReport(obj).subscribe(res => {
      if (res['result'] == 'success') {
        // this.reportNumber = res['reportNo']
        this.download(res);
        this.utils.openSnackBar('successfully uploaded', null, 3000);
      } 
    }, err => {
      console.log(err)
    })
  }

  saveC() {
    this.curItem.packagingSizes = this.packageItems;
    if (this.curItem.uom === 'portion') {
      this.curItem.uom = this.curItem.defaultUOM;
      this.curItem.price = this.curItem.actualPrice;
      this.curItem.inStock = this.curItem.currentStock;
      let conversionCoefficient = this.curItem.defaultUOM === 'NOS' ? 1 : 1000;
      let portionWeight = this.curItem.portionWeight * 1000
      let convertedWeight = this.utils.truncateNew((portionWeight  * this.curItem.closingStock) / (conversionCoefficient * 1000 ))
      this.curItem.closingStock = convertedWeight;
    }
    if (this.packageItems.length > 0) {
      this.curItem.packagingSizes.forEach((element) => {
        let obj = {
          currentWeight: element.otherPackages,
          packageQty: element.pkgQty,
          fullWeight: element.fullBottleWeight,
          emptyWeight: element.emptyBottleWeight,
          cnvrtWeightToCpt: element.cnvrtWeightToCpt,
          numOfOpenPkg: element.numOfOpenPkg,
        };
        element.cnvtdOtherPackages = this.utils.bottleWeightToCapacityConversion(
          obj
        );
      });

      let dialogClosingStock = 0;
      this.curItem.packagingSizes.forEach((item) => {
        item.orderedPackages = parseInt(item.orderedPackages, 10) || 0;
        item.cnvtdOtherPackages = this.utils.truncateNew(item.cnvtdOtherPackages) || 0;
        if (item.otherPackages) {
          dialogClosingStock =
            dialogClosingStock +
            item.orderedPackages * item.pkgQty +
            item.cnvtdOtherPackages;
        } else {
          dialogClosingStock =
            dialogClosingStock + item.orderedPackages * item.pkgQty;
        }
      });
    }
    this.inventory.saveClosingItem({
      data: this.savedClosingInfo,
      tenantId: this.user.tenantId,
      restId: this.restaurantId,
      email: this.user.email.replace(/\./g, '#'),
      userName: this.user.name,
      indentArea: this.indentArea,
      curItem: this.curItem,
      type: this.closingType,
      createTs: new Date(),
    })
      .subscribe((data) => {
        if (data != null) {
          this.utils.openSnackBar('Item Saved', null, 3000);
          this.savedClosingInfo.workAreas = data.workAreas;
          this.savedItems = this.savedClosingInfo.workAreas[this.indentArea].category[this.selectedCatVal].subCategory[this.selectedSubCatVal]['items']
          this.categoryStruct = this.savedClosingInfo.workAreas[this.indentArea].category
          this.subCategories = this.categoryStruct[this.selectedCatVal].subCategory
          this.displayPackages(this.dataSource.data[this.curItemIndex + 1], this.curItemIndex + 1)
        }
        else {
          this.utils.showInfo('Ah,there is some issue while saving.','', 3000)
        }
      });
  }

  sendReq(openExportDialog){
    const dialogRef = this.dialog.open(openExportDialog, { 
      autoFocus: false,
      disableClose: true,
      minWidth : '30vw',
      maxWidth : '35vw'
    });
  }

  cancel() {
    this.showItems = false;
    this.displayPkgFlag = false;
    this.inventory.cancelSubCat({
      data: this.savedClosingInfo,
      tenantId: this.user.tenantId,
      restId: this.restaurantId,
      email: this.user.email.replace(/\./g, '#'),
      indentArea: this.indentArea,
      cat: this.selectedCatVal,
      subCat: this.selectedSubCatVal,
      type: this.closingType
    }).subscribe((data) => {
      if (data != null) {
        this.utils.openSnackBar('changes cancelled', null, 3000);
        this.savedClosingInfo.workAreas = data.workAreas;
        this.categoryStruct = this.savedClosingInfo.workAreas[this.indentArea].category
        console.log(this.categoryStruct)
        this.subCategories = this.categoryStruct[this.selectedCatVal].subCategory
      }
      else {
        this.utils.snackBarShowError('Ah,there is some issue while cancelling.');
      }
    });

  }

  submitSubCat() {
    
    if (this.savedClosingInfo.workAreas[this.indentArea].category[this.selectedCatVal].subCategory[this.selectedSubCatVal]['curCount'] >= this.savedClosingInfo.workAreas[this.indentArea].category[this.selectedCatVal].subCategory[this.selectedSubCatVal]['initCount']) {
      this.utils.openSnackBar('successfully submitted', null, 3000);
      this.showItems = false;
      this.displayPkgFlag = false;
    }
    else {
      this.utils.snackBarShowError('Please complete all items for this subcategory');
    }
  }

  save() {
    this.curItem.packagingSizes = this.packageItems;
    let newObj = {};
    if (!this.savedItems.hasOwnProperty(this.curItem.itemCode)) {
      let curCategory = this.curItem.category;
      this.catCount[curCategory]["curCount"] = this.catCount[curCategory]["curCount"] + 1;
      this.catCount[curCategory]["percentage"] = this.utils.truncateNew((this.catCount[curCategory]["curCount"] / this.catCount[curCategory]["initCount"]) * 100);
    }
    this.modifyCatStruct(this.category_new);

    newObj["tenantId"] = this.user.tenantId;
    newObj["restId"] = this.restaurantId;
    newObj["userEmail"] = this.user.email;
    newObj["catDetails"] = this.catCount;
    newObj["items"] = {};
    newObj["items"][this.curItem.itemCode] = {};
    newObj["items"][this.curItem.itemCode]["name"] = this.curItem.itemName;
    if (this.packageItems.length > 0) {
      newObj["items"][this.curItem.itemCode]["packagingSizes"] = this.packageItems;
    } else {
      newObj["items"][this.curItem.itemCode]["closingStock"] = this.curItem.closingStock;
    }
    newObj["items"][this.curItem.itemCode]["category"] = this.curItem.category;
    newObj["items"][this.curItem.itemCode]["subCategory"] = this.curItem.subCategory;
    newObj["items"][this.curItem.itemCode]["isComplete"] = true;
    this.inventory
      .saveTempClosing({
        data: newObj,
      })
      .subscribe((data) => {
        this.savedItems = data.savedItems;
      });

    let stop = false;
    let itemCompleted = 0;
    let totalItem = 0;
    this.init_category_new.forEach((element) => {
      itemCompleted = itemCompleted + this.catCount[element]["curCount"];
      totalItem = totalItem + this.catCount[element]["initCount"];
      if (this.catCount[element]["percentage"] == 100 && !stop) {
        this.closingCompleted = true;
      } else {
        this.closingCompleted = false;
        stop = true;
      }
    });
    this.overallProgress = (itemCompleted / totalItem) * 100;
    if (this.packageItems.length > 0) {
      this.curItem.packagingSizes.forEach((element) => {
        let obj = {
          currentWeight: element.otherPackages,
          packageQty: this.curItem.moq,
          fullWeight: this.curItem.fullBottleWeight,
          emptyWeight: element.emptyBottleWeight,
          cnvrtWeightToCpt: element.cnvrtWeightToCpt,
          numOfOpenPkg: element.numOfOpenPkg,
        };
        element.cnvtdOtherPackages = this.utils.bottleWeightToCapacityConversion(obj);
      });

      let dialogClosingStock = 0;
      this.curItem.packagingSizes.forEach((item) => {
        item.orderedPackages = parseInt(item.orderedPackages, 10) || 0;
        item.cnvtdOtherPackages = this.utils.truncateNew(item.cnvtdOtherPackages) || 0;
        if (item.otherPackages) {
          dialogClosingStock =
            dialogClosingStock +
            item.orderedPackages * item.pkgQty +
            item.cnvtdOtherPackages;
        } else {
          dialogClosingStock =
            dialogClosingStock + item.orderedPackages * item.pkgQty;
        }
      });
      this.curItem.closingStock = this.utils.truncateNew(dialogClosingStock);
    }
    this.updateClosingStock();
  }

  universalFilter() {
    let arr = this.workAreaItemsList;
    if (
      this.selectedCategory === "ALL" &&
      this.selectedSubCategory === "ALL" &&
      this.selectedItemType === "ALL"
    ) {
      this.dataSource.data = arr;
    } else if (
      this.selectedCategory !== "ALL" &&
      this.selectedSubCategory !== "ALL" &&
      this.selectedItemType !== "ALL"
    ) {
      let arr1 = arr.filter(
        (item) =>
          item["ItemType"].toUpperCase() === this.selectedItemType.toUpperCase()
      );
      let arr2 = arr1.filter(
        (item) =>
          item["subCategory"].toUpperCase() ===
          this.selectedSubCategory.toUpperCase()
      );
      this.dataSource.data = arr2.filter(
        (item) =>
          item["category"].toUpperCase() === this.selectedCategory.toUpperCase()
      );
    } else if (
      this.selectedCategory !== "ALL" &&
      this.selectedSubCategory !== "ALL" &&
      this.selectedItemType === "ALL"
    ) {
      let arr1 = arr.filter(
        (item) =>
          item["subCategory"].toUpperCase() ===
          this.selectedSubCategory.toUpperCase()
      );
      this.dataSource.data = arr1.filter(
        (item) =>
          item["category"].toUpperCase() === this.selectedCategory.toUpperCase()
      );
    } else if (
      this.selectedCategory !== "ALL" &&
      this.selectedSubCategory === "ALL" &&
      this.selectedItemType !== "ALL"
    ) {
      let arr1 = arr.filter(
        (item) =>
          item["ItemType"].toUpperCase() === this.selectedItemType.toUpperCase()
      );
      this.dataSource.data = arr1.filter(
        (item) =>
          item["category"].toUpperCase() === this.selectedCategory.toUpperCase()
      );
    } else if (
      this.selectedCategory !== "ALL" &&
      this.selectedSubCategory === "ALL" &&
      this.selectedItemType === "ALL"
    ) {
      this.dataSource.data = arr.filter(
        (item) =>
          item["category"].toUpperCase() === this.selectedCategory.toUpperCase()
      );
    } else if (
      this.selectedCategory === "ALL" &&
      this.selectedSubCategory === "ALL" &&
      this.selectedItemType !== "ALL"
    ) {
      this.dataSource.data = arr.filter(
        (item) =>
          item["ItemType"].toUpperCase() === this.selectedItemType.toUpperCase()
      );
    } else if (
      this.selectedCategory === "ALL" &&
      this.selectedSubCategory !== "ALL" &&
      this.selectedItemType === "ALL"
    ) {
      this.dataSource.data = arr.filter(
        (item) =>
          item["subCategory"].toUpperCase() ===
          this.selectedSubCategory.toUpperCase()
      );
    } else if (
      this.selectedCategory === "ALL" &&
      this.selectedSubCategory !== "ALL" &&
      this.selectedItemType !== "ALL"
    ) {
      let arr1 = arr.filter(
        (item) =>
          item["ItemType"].toUpperCase() === this.selectedItemType.toUpperCase()
      );
      this.dataSource.data = arr1.filter(
        (item) =>
          item["subCategory"].toUpperCase() ===
          this.selectedSubCategory.toUpperCase()
      );
    }
  }

  filter_category_option() {
    let filtered_category_new = [];
    let tmp_arr = this.inventoryItems;
    let tmp_arr1 = tmp_arr.filter(
      (item) =>
        item["ItemType"].toUpperCase() === this.selectedItemType.toUpperCase()
    );
    tmp_arr1.forEach((item) => {
      filtered_category_new.push(item.category);
    });
    filtered_category_new = filtered_category_new.filter(
      (k, i, ar) => ar.indexOf(k) === i
    );
    this.category_new = filtered_category_new;
    this.modifyCatStruct(this.category_new);
  }

  modifyCatStruct(cat) {
    this.categoryStruct = this.savedClosingInfo.workAreas[this.indentArea].category;
  }

  filter_subCategory_option() {
    let filtered_subcategory_new = [];
    let tmp_arr = this.inventoryItems;
    let tmp_arr1 = tmp_arr.filter(
      (item) =>
        item["category"].toUpperCase() === this.selectedCategory.toUpperCase()
    );
    tmp_arr1.forEach((item) => {
      filtered_subcategory_new.push(item.subCategory);
    });
    filtered_subcategory_new = filtered_subcategory_new.filter(
      (k, i, ar) => ar.indexOf(k) === i
    );
  }

  selectItemType(val) {
    this.selectedItemType = val.value;
    this.selection.clear();
    switch (val.value) {
      case "All":
        this.selectedItemType = "ALL";
        break;
      case "Menu Items":
        this.selectedItemType = "Menu";
        break;
      case "Inventory":
        this.selectedItemType = "Inventory";
        break;
      case "Sub Receipe":
        this.selectedItemType = "SubRecipe";
        break;
    }
    if (this.selectedItemType !== "ALL") {
      this.filter_category_option();
    }
    if (this.selectedItemType === "ALL") {
      this.category_new = this.init_category_new;
    }
    this.universalFilter();
  }

  updateClosingStock() {
    let tmpArray = [];
    tmpArray.push(this.curItem);
    let updatedItems = tmpArray
      .map((item) => {
        let obj: any = { closingStock: item.closingStock };
        if ((!this.toggleToWorkArea) && (this.router.url.includes(this.closingStockUrl) || this.router.url.includes(this.transferClosingUrl))) {
          obj.inStock = item.closingStock;
          if (this.router.url.includes(this.adjustInventoryUrl)) {
            if (item.adjustReason && item.adjustReason.increment)
              obj.inStock = item.inStock + item.closingStock;
            else obj.inStock = item.inStock - item.closingStock;
          }
          obj.inKitchen = item.inKitchen;
        } else if (this.toggleToWorkArea || (this.user.role.includes(GlobalsService.kitchenManager))) {
          obj.inKitchen = item.closingStock;
          if (item.menuWeight) {
            obj.inKitchen = this.utils.truncateNew(item.closingStock / item.menuWeight);

          }
          if (this.router.url.includes(this.adjustInventoryUrl)) {
            if (item.adjustReason.increment)
              obj.inKitchen = item.inKitchen + item.closingStock;
            else obj.inKitchen = item.inKitchen - item.closingStock;
          }
        }
        obj.mId = item.mId;
        if (item.adjustReason) obj.adjustReason = item.adjustReason.name;
        else obj.adjustReason = "";
        return obj;
      })
      .filter((item) => item.closingStock != undefined);

    let reqObj: any = {
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId,
      updatedList: updatedItems,
      indentArea: this.indentArea,
      uId: this.user.mId,
    };
    if (this.router.url.includes(this.closingStockUrl) || this.router.url.includes(this.transferClosingUrl)) {
      if (!this.toggleToWorkArea) {
        reqObj.isStore = true;
      } else {
        reqObj.isStore = false;
      }
    }
    else reqObj.isStore = false;
    if (!this.router.url.includes(this.adjustInventoryUrl))
      reqObj.adjustInventory = false;
    else reqObj.adjustInventory = true;

    this.inventory.updateClosingStock(reqObj).subscribe(
      (data) => {
        if (data.result === GlobalsService.success) {
          this.utils.openSnackBar(data.desc, null, 3000);
        }
      },
      (err) => {
        console.error(err);
      }
    );
  }

  checkboxLabel(row?: any): string {
    if (!row) {
      return `${this.isAllSelected() ? "select" : "deselect"} all`;
    }
    return `${this.selection.isSelected(row) ? "deselect" : "select"} row ${row.itemCode + 1
      }`;
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.data.forEach((row) => this.selection.select(row));
  }
  adjustInventory() {
    this.utils.showInfo("Adjust Beeech",'', 3000)
  }


  goBack() {
    this.showItems = false;
    this.displayPkgFlag = false;
  }

  exportToExcel() {
    var today = new Date();
    var date =
      today.getFullYear() +
      "-" +
      (today.getMonth() + 1) +
      "-" +
      today.getDate();
    let fileName = "StockClosureDetails" + date;
    this.downloadFile(this.dataSource.data, fileName);
  }

  downloadFile(data, filename = "data") {
    let csvData = this.ConvertToCSV(data, [
      "itemName",
      "inStock",
      "inKitchen",
      "uom",
    ]);
    let blob = new Blob(["\ufeff" + csvData], {
      type: "text/csv;charset=utf-8;",
    });
    let dwldLink = document.createElement("a");
    let url = URL.createObjectURL(blob);
    let isSafariBrowser =
      navigator.userAgent.indexOf("Safari") != -1 &&
      navigator.userAgent.indexOf("Chrome") == -1;
    if (isSafariBrowser) {
      dwldLink.setAttribute("target", "_blank");
    }
    dwldLink.setAttribute("href", url);
    dwldLink.setAttribute("download", filename + ".csv");
    dwldLink.style.visibility = "hidden";
    document.body.appendChild(dwldLink);
    dwldLink.click();
    document.body.removeChild(dwldLink);
  }

  ConvertToCSV(objArray, headerList) {
    let array = typeof objArray != "object" ? JSON.parse(objArray) : objArray;
    let str = "";
    let row = "S.No,";
    for (let index in headerList) {
      row += headerList[index] + ",";
    }
    row = row.slice(0, -1);
    str += row + "\r\n";
    for (let i = 0; i < array.length; i++) {
      let line = i + 1 + "";
      for (let index in headerList) {
        let head = headerList[index];

        line += "," + array[i][head];
      }
      str += line + "\r\n";
    }
    return str;
  }

  selectActiveLink(type, i) {
    if (type == "cat") {
      Object.keys(this.categoryStruct).forEach((x) => (this.categoryStruct[x].isSelected = false));
      this.categoryStruct[this.selectedCatVal].isSelected = true;
    } else if (type == "item") {
      this.dataSource.data.forEach((x) => (x.isSelected = false));
      this.dataSource.data[i].isSelected = true;
    }
  }

  filterByBranch(restId) {
    this.currentBranchName = restId;    
    this.restaurantId = restId.restaurantIdOld;
    this.branchSelected = true;
    if (this.transferClosing == true) {
        this.filteredWorkAreasList = ["store"];
        const workareaValue =  "store";
        this.currentWorkAreaName = []
        this.closingForm.get('workAreaSelection').setValue("store");
        this.VendorBank = this.filteredWorkAreasList ;
        this.vendorsBanks.next(this.VendorBank.slice());
        this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.vendorfilterBanks();
        });
        this.selectIndentArea(workareaValue);
    } else {
      this.user.restaurantAccess.forEach(element => {
        if (element.restaurantIdOld == this.restaurantId) {
          this.filteredWorkAreasList = element.workAreas;
        }
      });
      this.VendorBank = this.filteredWorkAreasList ;
      this.vendorsBanks.next(this.VendorBank.slice());
      this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
          this.vendorfilterBanks();
      });
    }


  }

  protected vendorfilterBanks() {
    if (!this.VendorBank) {
      return;
    }
    let search = this.vendorFilterCtrl.value;
    if (!search) {
      this.vendorsBanks.next(this.VendorBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.vendorsBanks.next(
      this.VendorBank.filter(VendorBank => VendorBank.toLowerCase().indexOf(search) > -1)
    );
  }

  selectIndentArea(val) {
    this.currentWorkAreaName = val;
    this.indentArea = val;
    this.category.setValue("");
    this.subCategory.setValue("");
    this.itemTypeForm.setValue("");
    this.newFunction();
    this.getJobs();
  }

  newFunction() {
    this.loading = true;
    let newObj = {
        tenantId: this.user.tenantId,
        workArea: this.indentArea,
        restaurantId: this.restaurantId,
        uId: this.user.mId,
        reqQty: false,
        isClosing: true,
        userEmail: this.user.email
      };
      if (this.router.url.includes(this.transferClosingUrl)) {
          newObj['transfer'] = true;
          if (!this.toggleToWorkArea) {
              newObj['isStoreClosing'] = true;
              this.closingType = 'storeClosing';
          } else {
              newObj['isKitchenClosing'] = true;
              this.closingType = 'kitchenClosing';
          }
      } else if (this.router.url.includes(this.closingStockUrl)) {
          newObj['transfer'] = false;
          if (!this.toggleToWorkArea) {
              newObj['isStoreClosing'] = true;
              this.closingType = 'storeClosing';
          } else {
              newObj['isKitchenClosing'] = true;
              this.closingType = 'kitchenClosing';
          }
      } else if (this.router.url.includes(this.kitchenClosingUrl)) {
          newObj['isKitchenClosing'] = true;
          this.closingType = 'kitchenClosing';
      }
      this.branchTransfer.getBranchInvNew(newObj).subscribe(data => {     
      if (data) {
        this.showSubCat = false;
        this.displayMessage = 'Select a Subcategory to continue'
        this.dataSource = new MatTableDataSource<any>();
        this.dataSource.sort = this.sort;
        if (data.hasOwnProperty("savedClosingInfo")) {
          this.savedClosingInfo = data.savedClosingInfo
        }
        else {
          this.savedClosingInfo = { 'workAreas': data.catObj }
        }
        if (this.router.url.includes(GlobalsService.adjustInventory)) {
          this.displayedColumns = GlobalsService.adjustInventoryColumns
        } else {
          this.displayedColumns = GlobalsService.kitchenClosingColumns
        }
        if (this.router.url.includes(this.kitchenClosingUrl)) {
          this.user.restaurantAccess.forEach(element => {
            if (element.restaurantIdOld == this.restaurantId) {
              this.indentAreas = element.workAreas
              if (element.workAreas == undefined) {
                this.indentAreas = data.workAreas;
              }

            }
          });
        }
        else if (this.router.url.includes(this.closingStockUrl) || this.router.url.includes(this.transferClosingUrl)) {
          if (!this.toggleToWorkArea) {
            this.indentAreas = ['store'];
            this.indentArea = 'store';
          } else {
            this.user.restaurantAccess.forEach(element => {
              if (element.restaurantIdOld == this.restaurantId) {
                this.indentAreas = element.workAreas
                if (element.workAreas == undefined) {
                  this.indentAreas = data.workAreas;
                }
              }
            });
          }
        }
        this.filteredWorkAreasList = this.indentAreas
        this.workAreaItemsList = data.invItems;
        this.inventoryItems = data.invItems.map((item: any) => {
          if (item.servingSize)
            item.displayName = `${item.itemName} | ${item.servingSize}`;
          else item.displayName = item.itemName;
          if (item.ItemType == "Menu") {
            item.uom = item.closingUom;
          } else {
            item.uom = item.uom;
          }

          if (item.closingUom != null) {
            if (item.closingUom.toLowerCase().includes("open")) {
              item.displayFreePackage = true;
            } else {
              item.displayFreePackage = false;
            }
          }
          return item;
        });
        if (this.router.url.includes(this.closingStockUrl) || this.router.url.includes(this.transferClosingUrl)) {
          this.itemTypes = ["All", "Inventory", "Sub Receipe"];
          let temp_arr = this.inventoryItems;
          this.inventoryItems = temp_arr.filter(
            (item) => item["ItemType"].toUpperCase() != "MENU"
          );
        }
        this.dataSource.data = this.inventoryItems;
        this.categories = [];
        this.inventoryItems.forEach((item) => {
          item['defaultUOM'] = item['uom']
          item['currentStock'] = item['inStock']
          item['actualPrice'] = item['price']
          this.invCategories.push(item.invCategory);
          this.menuGroups.push(item.menuGroup);
          if (item.category == null) {
            item.category = "N/A";
          }
          this.category_new.push(item.category);
          if (item.ItemType == null) {
            item.ItemType = "N/A";
          }
          if (item.subCategory == null) {
            item.subCategory = "N/A";
          }
          this.ItemType.push(item.ItemType);
        });

        this.category_new = this.category_new.filter(
          (k, i, ar) => ar.indexOf(k) === i
        );
        this.ItemType = this.ItemType.filter(
          (p, q, arrr) => arrr.indexOf(p) === q
        );
        this.init_category_new = this.category_new;
        this.invCategories = this.utils
          .getUniqueItems(this.invCategories)
          .sort((a, b) => a.name > b.name)
          .filter((c) => c.name);
        this.menuGroups = this.utils
          .getUniqueItems(this.menuGroups)
          .sort((a, b) => a.name > b.name)
          .filter((c) => c.name);
        this.allCategories = [...this.invCategories, ...this.menuGroups];
        this.categories = this.allCategories;
        this.workAreaItemsList = this.inventoryItems;
        this.pageSizes = this.utils.getPageSizes(this.inventoryItems);
        if (this.savedClosingInfo.workAreas.hasOwnProperty(this.indentArea)) {
          this.catCount = this.savedClosingInfo.workAreas[this.indentArea].category;
          this.modifyCatStruct(this.category_new);
          this.showCatList = true
          this.displayMessage = 'Select a Subcategory to continue'
          if (this.router.url.includes(this.kitchenClosingUrl)) {
            this.workAreaItemsList = this.inventoryItems.filter((item) =>
              Object.keys(item.workArea).includes(this.indentArea)
            );
            this.dataSource.data = this.workAreaItemsList;
          }
          else {
            this.dataSource.data = this.inventoryItems
          }
          this.categoryStruct = this.savedClosingInfo.workAreas[this.indentArea].category;
        }
        else {
          this.showCatList = false
          this.displayMessage = 'Selected workArea has no any items mapped.Please select another workArea'
        }
      }
      this.loading = false;
    }, err => console.error(err))
    this.displayPkgFlag = false
    this.showSubCat = false
    this.entryAccess = false;
  }

  increment(val, i) {
    val = val + 1
  }

  addOpenPkg(ps) {
    let openPkg = { ...ps };
    openPkg.otherPackages = 0;
    openPkg.orderedPackages = 0;
    openPkg.numOfOpenPkg = 0;
    openPkg.onlyOpen = true
    this.packageItems.push(openPkg)
  }

  getTotalIndentCostQuantity(curItem) {    
    if ( curItem['uom'] == 'portion'){
     let conversionCoefficient =  curItem['defaultUOM'] == 'NOS' ? 1: 1000 ;
     let portionWeight = curItem.portionWeight * 1000
     curItem['convertedWeight'] = this.utils.truncateNew((portionWeight  * curItem.closingStock) / (conversionCoefficient * 1000 ))
    }
 
   }
 
   convertToQty(el){
     if (el['selectedOption'] === 'portion')  {
       let conversionCoefficient =  el['defaultUOM'] == 'NOS' ? 1: 1000 ;
       el['uom'] = 'portion'
       el['inStock'] = this.utils.truncateNew(el['currentStock'] / (el.portionWeight / conversionCoefficient))
       el['price'] = this.utils.truncateNew(el['actualPrice'] / (el.price / conversionCoefficient))
       let portionWeight = el.portionWeight * 1000
       el['convertedWeight'] = this.utils.truncateNew((portionWeight  * el.closingStock) / (conversionCoefficient * 1000 ))
     } else {
       el['uom'] = el['defaultUOM']
       el['inStock'] = el['currentStock']
       el['price'] = el['actualPrice']
     }
   }

  redirect() {
    if (this.router.url.includes(this.transferClosingUrl)) {
      this.router.routeReuseStrategy.shouldReuseRoute = () => false;
      this.router.onSameUrlNavigation = 'reload';
      this.router.navigate(['/home/<USER>']);
    }
    else {
      if (this.closingType == 'storeClosing') {
        this.router.routeReuseStrategy.shouldReuseRoute = () => false;
        this.router.onSameUrlNavigation = 'reload';
        this.router.navigate(['/home/<USER>']);
      }
      else {
        this.router.routeReuseStrategy.shouldReuseRoute = () => false;
        this.router.onSameUrlNavigation = 'reload';
        this.router.navigate(['/home/<USER>']);
      }
    }
  }

  filterWorkArea() {
    this.filteredWorkAreasList = this.indentAreas.filter(
      (wa) =>
        wa.toLowerCase().includes(this.searchText.toLowerCase())
    );
  }

  decreaseValue(ps){
    if(ps.orderedPackages < 0 || ps.orderedPackages == null || ps.orderedPackages == 0 ){
      ps.orderedPackages = null
    }else{
      ps.orderedPackages = ps.orderedPackages - 1
    }
  }

  focusFunctionWithOutForm(element){
    if(Number(element.orderedPackages) === 0){
      element.orderedPackages = null;
    }
  }
  
  focusOutFunctionWithOutForm(element){
    if(element.orderedPackages === null){
      element.orderedPackages = 0
    }
  }

  //######################  toaster code ###################### // 
  showToasterSuccess(data) {
    // this.notifyService.showSuccess(data, "RMS")
    this.utils.snackBarShowSuccess(data);
  }
  showToasterError(data) {
    // this.notifyService.showError(data, "RMS")
    this.utils.snackBarShowError(data);
  }
  showToasterInfo(data) {
    // this.notifyService.showInfo(data, "RMS")
    this.utils.snackBarShowInfo(data)
  }
  showToasterWarning(data) {
    // this.notifyService.showWarning(data, "RMS")
    this.utils.snackBarShowWarning(data);
  }

  selectedSource(val){
    if(val === 'UI'){
      this.jobs.get('jobs').setValue(this.events.find((c) => c.id == 1));
      this.filterJobsData()
    }else{
      this.jobs.get('jobs').setValue(this.events.find((c) => c.id == 1));
      this.getJobs()
    }
  }

  onMouseOver(hoveredData){    
    this.showTooltip = true;  
    this.hoveredElement = hoveredData;             
  }

  onMouseOut(){
    this.showTooltip = false;
  }

}