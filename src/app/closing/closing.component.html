<form [formGroup]="closingForm" class="topHeadInputs">
  <mat-form-field *ngIf="entryAccess" appearance="none" class="selectItem">
    <label>Select Branch</label>
    <mat-select placeholder="Select Branch" class="outline" formControlName="branchSelection"
      (selectionChange)="filterByBranch($event.value)">
      <mat-option *ngFor="let rest of branches" [value]="rest">
        {{ rest.branchName }}
      </mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="none" *ngIf="entryAccess" style="margin-left: 10px;" class="selectItem">
    <label>Select Work Area</label>
    <mat-select placeholder="Select Work Area" formControlName="workAreaSelection"
      (selectionChange)="selectIndentArea($event.value)" class="outline">
      <mat-option>
        <ngx-mat-select-search placeholderLabel="Work Area..." noEntriesFoundLabel="'Work Area Not Found'"
          [formControl]="vendorFilterCtrl"></ngx-mat-select-search>
      </mat-option>
      <mat-option *ngFor="let area of vendorsBanks | async" [value]="area">
        {{ area }}
      </mat-option>
    </mat-select>
  </mat-form-field>
</form>


<div *ngIf="!entryAccess">
  <mat-card *ngIf="!finalSubmit">

    <div class="infoMessage" *ngIf="this.router.url.includes(this.closingStockUrl)">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
        class="bi bi-info-circle-fill infoSvgIcon" viewBox="0 0 16 16">
        <path
          d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z" />
      </svg>
      <p class="ml-2 mb-0"> It's adviced to select the closing date as either current or previous date</p>
    </div>

    <mat-tab-group>
      <mat-tab>
        <ng-template mat-tab-label>Closing</ng-template>
        <div *ngIf="!finalSubmit">
          <form [formGroup]="closingForm" class="topHeadInputs">
            <mat-form-field appearance="none" class="mr-2">
              <label>Select Branch</label>
              <mat-select placeholder="Select Branch" class="outline" formControlName="branchSelection"
                [(ngModel)]='this.currentBranchName' (selectionChange)="filterByBranch($event.value)">
                <mat-option *ngFor="let rest of branches" [value]="rest">
                  {{ rest.branchName }}
                </mat-option>
              </mat-select>
            </mat-form-field>

            <mat-form-field appearance="none"  class="mr-2">
              <label>Select Work Area</label>
              <mat-select placeholder="Select Work Area" formControlName="workAreaSelection"
                (selectionChange)="selectIndentArea($event.value)" class="outline">
                <mat-option>
                  <ngx-mat-select-search placeholderLabel="Work Area..." noEntriesFoundLabel="'Work Area Not Found'"
                    [formControl]="vendorFilterCtrl"></ngx-mat-select-search>
                </mat-option>
                <mat-option *ngFor="let area of vendorsBanks | async" [value]="area">
                  {{ area }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </form>

          <mat-form-field appearance="none" class="mr-2">
            <label>Closing Date</label>
            <input matInput class="outline" [matDatepicker]="picker1" [min]="yesterday" [max]="todayDate"
              placeholder="Closing Date" [formControl]="closingDate" />
            <mat-datepicker-toggle matSuffix [for]="picker1">
              <mat-icon matDatepickerToggleIcon>
                <img src="./../../assets/calender.png" />
              </mat-icon>
            </mat-datepicker-toggle>
            <mat-datepicker #picker1></mat-datepicker>
          </mat-form-field>
          <button mat-stroked-button class="button mr-2 mb-1 fourButtons" matTooltip="Upload Template"
            *ngIf="!this.router.url.includes(this.transferClosingUrl)" (click)="templateFileInput.value=''; uploadTemplate()">
            Import Template <mat-icon> upload </mat-icon>
          </button>
          <button mat-stroked-button class="button mr-2 mb-1 fourButtons" matTooltip="Upload & Update Template"
            *ngIf="!this.router.url.includes(this.transferClosingUrl)" (click)="fileInput.value=''; checkUpload()">
            Import <mat-icon> upload </mat-icon>
          </button>
          <button mat-stroked-button class="button mr-2 mb-1 fourButtons" matTooltip="Generate Template"
            (click)="sendReq(openExportDialog)" *ngIf="!this.router.url.includes(this.transferClosingUrl)">
            Export
            <mat-icon>download </mat-icon>
          </button>
          <input #fileInput (change)="getFileFormatForClosing($event)" accept=".xlsx, .xls" type='file' id="getFile"
            style="display:none">
          <input #templateFileInput (change)="handleTemplateUpload($event)" accept=".xlsx, .xls" type='file' id="getTemplateFile"
            style="display:none">

          <!-- <input  (change)="fileChangeListener($event)" style="display: none;" />
              accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" -->

          <button mat-stroked-button class="button  mr-2 mb-1 fourButtons"
            *ngIf="!this.router.url.includes(this.transferClosingUrl)" (click)="setClosingStock()">
            Update
          </button>

          <button mat-stroked-button class="button mr-2 mb-1 fourButtons"
            *ngIf="this.router.url.includes(this.transferClosingUrl)" (click)="ckClosing()">
            Transfer
          </button>
        </div>

        <div class="title">
          <div *ngIf="showItems">
            <div class="fieldbox4-a">
              <span class="label5">{{selectedCatVal|titlecase}} > {{selectedSubCatVal|titlecase}}</span>
            </div>
            <button mat-raised-button class="button3" style="float: right;" (click)="cancel()">
              Cancel
            </button>
            <button mat-raised-button class="button3" style="float: right;" (click)="submitSubCat()">
              Submit
            </button>
          </div>
        </div>

        <div>
          <label class="ipText">Default transfer by :  </label>
          <mat-radio-group [(ngModel)]="selectedOption" (change)="portionChange()">
            <mat-radio-button [value]="false">UOM</mat-radio-button>
            <mat-radio-button [value]="true">PORTION</mat-radio-button>
          </mat-radio-group>
        </div>       
                     
        <div *ngIf="loading" class="closingLoadingContainer">
          <div class=" closingLoadingContainerDatas">
            <div class="loader">
              <mat-spinner [diameter]="40" mode="indeterminate" overlay="true"> </mat-spinner>
            </div>
            <div>
              <mat-hint *ngIf="this.router.url.includes(this.kitchenClosingUrl)">We are fetching your data, it usually
                takes 5 to 10 seconds... </mat-hint>
              <mat-hint
                *ngIf="this.router.url.includes(this.closingStockUrl) || this.router.url.includes(this.transferClosingUrl)">We
                are fetching your data, it usually takes 50 to 60 seconds... </mat-hint>
            </div>
          </div>
        </div>
        <mat-card  class="containerout border" *ngIf="!finalSubmit && loading == false" style="width: 100%;">
          <mat-card-content *ngIf="!data ">
            <div *ngIf="!data">
              <div class="row">
                <div class="left">
                  <mat-accordion multi="true" *ngIf="!showItems && showCatList">
                    <mat-expansion-panel hideToggle="true" [expanded]="true" disabled class="left-body">
                      <mat-expansion-panel-header>
                        <div class="cat">Select Category</div>
                      </mat-expansion-panel-header>
                      <mat-expansion-panel-header class="panel-header">
                        <label class="cat cloasingLabel">
                          Mandatory Category
                        </label>
                        <label class="inlspn5" style="margin-bottom: 5px;">
                          You must finish all categories to complete stock taking
                        </label>
                      </mat-expansion-panel-header>
                      <mat-nav-list style="margin-right: 16px;">
                        <div *ngFor="let cat of categoryStruct|keyvalue; let i = index" class="catagory">
                          <a mat-list-item (click)="selectCategory(cat.key, i)"
                            [ngClass]="{ 'active-link': cat.isSelected }">{{
                            cat.key | titlecase }}

                            <label *ngIf="cat.value.percentage == 0 && !cat.isSelected" class="fade_text pull-right"
                              style="float: right;">0% Done</label>
                            <label *ngIf="cat.value.percentage != 0 && !cat.isSelected" class="success_text pull-right"
                              style="float: right;">{{ cat.value.percentage }}% Done</label>
                            <mat-progress-bar *ngIf="!cat.isSelected && cat.percntg != 0" mode="determinate"
                              value="{{ cat.value.percentage }}" class="progressbar_small"></mat-progress-bar>
                          </a>
                        </div>
                      </mat-nav-list>
                    </mat-expansion-panel>
                  </mat-accordion>

                  <mat-accordion multi="true" *ngIf="showItems">
                    <mat-expansion-panel hideToggle="true" [expanded]="true" disabled class="left-body">
                      <mat-expansion-panel-header>
                        <div class="cat">Select Item</div>
                      </mat-expansion-panel-header>

                      <mat-nav-list style="margin-right: 16px;">
                        <cdk-virtual-scroll-viewport itemSize="36" class="scrollView" minBufferPx="72"
                          maxBufferPx="144">
                          <div *cdkVirtualFor="let row of dataSource; let i = index" class="catagory">
                            <a mat-list-item (click)="displayPackages(row, i)"
                              [ngClass]="{ 'active-link': row.isSelected }">
                              <div class="row">
                                <div class="col-10">
                                  {{row.itemName | titlecase }}
                                </div>
                                <div class="col-2">
                                  <label *ngIf="savedItems[row.itemCode] == null && !row.isSelected"
                                    class="fade_text pull-right" style="float: right;">Pending..</label>
                                  <label *ngIf="savedItems[row.itemCode] != null && !row.isSelected"
                                    class="success_text pull-right" style="float: right;">Completed</label>
                                </div>
                              </div>
                            </a>
                          </div>
                        </cdk-virtual-scroll-viewport>
                      </mat-nav-list>
                    </mat-expansion-panel>
                  </mat-accordion>
                </div>

                <div class="right">
                  <mat-accordion multi="true" *ngIf="!displayPkgFlag">
                    <mat-expansion-panel hideToggle="true" [expanded]="true" disabled>
                      <mat-expansion-panel-header>
                        <div class="cat">
                          Select Sub Category
                        </div>
                      </mat-expansion-panel-header>

                      <mat-nav-list *ngIf="showSubCat;else showMsg">
                        <div class="subcat-container" *ngFor="let subCat of subCategories|keyvalue">
                          <div class="subcat-box"
                            *ngIf="!subCat.value.underProcess && !(subCat.value.percentage == 100)">
                            <a mat-list-item routerLinkActive="active-link" (click)="selectSubCategory(subCat.key)">
                              <div class="ellipse">
                                <div class="arrow">
                                  <i class="fa fa-play" aria-hidden="true"></i>
                                </div>
                              </div>
                              <div class="inner_heading">{{ subCat.key | titlecase }}</div>
                              <div class="inner_heading2">{{ subCat.value.initCount-subCat.value.curCount }} Items
                                Remaining
                              </div>
                            </a>
                          </div>

                          <div class="subcat-box2"
                            *ngIf="subCat.value.underProcess && !(subCat.value.percentage == 100)">
                            <a mat-list-item routerLinkActive="active-link" (click)="selectSubCategory(subCat.key)">
                              <div class="ellipse1">
                                <div class="arrow1">
                                  <i class="fa fa-lock" aria-hidden="true"></i>
                                </div>
                              </div>
                              <div class="inner_heading">{{ subCat.key | titlecase }}</div>
                              <div class="inner_heading2" style="color: red;">
                                {{subCat.value.userName}} is editing
                              </div>
                            </a>
                          </div>

                          <div class="subcat-box2" *ngIf="subCat.value.percentage == 100">
                            <a mat-list-item routerLinkActive="active-link" (click)="selectSubCategory(subCat.key)">
                              <div class="ellipse2">
                                <div class="arrow2">
                                  <i class="fa fa-check" aria-hidden="true"></i>
                                </div>
                              </div>
                              <div class="inner_heading">{{ subCat.key | titlecase }}</div>
                              <div class="inner_heading2" style="color: green;">
                                Completed by {{subCat.value.userName}}
                              </div>
                            </a>
                          </div>
                        </div>
                      </mat-nav-list>
                      <ng-template #showMsg>
                        <div class="empty_select">
                          {{displayMessage}}</div>
                      </ng-template>
                    </mat-expansion-panel>
                  </mat-accordion>

                  <div class="tablestock" *ngIf="displayPkgFlag">
                    <div class="stockheading">Enter Closing Stock</div>
                    <div class="stockbody">
                      <div *ngIf="packageItems.length > 0; else noPackage">
                        <mat-grid-list cols="4" rowHeight="30px">
                          <mat-grid-tile>Package Name </mat-grid-tile>
                          <mat-grid-tile>Num Of Packages</mat-grid-tile>
                          <mat-grid-tile>Package Quantity </mat-grid-tile>
                          <mat-grid-tile>UOM</mat-grid-tile>
                        </mat-grid-list>
                        <mat-grid-list class="package_items" cols="4" *ngFor="let ps of packageItems; let i = index"
                          rowHeight="30px">
                          <div *ngIf="!ps.onlyOpen">
                            <mat-grid-tile>{{ ps.pkgName }}</mat-grid-tile>
                            <mat-grid-tile>
                              <button class="value-button" id="decrease" value="Decrease Value"
                                (click)="decreaseValue(ps)">
                                -
                              </button>
                              <input matInput [(ngModel)]="ps.orderedPackages" type="number" step="0.01" min="0"
                                placeholder="{{ ps.orderedPackages || 0 }}" step="any" class="input1"
                                (focus)="focusFunctionWithOutForm(ps)" (focusout)="focusOutFunctionWithOutForm(ps)" />
                              <!-- onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"  -->
                              <button class="value-button" id="increase"
                                (click)="ps.orderedPackages = ps.orderedPackages + 1" value="Increase Value">+</button>
                            </mat-grid-tile>
                            <mat-grid-tile> {{ ps.pkgQty }} </mat-grid-tile>
                            <mat-grid-tile> {{ curItem.uom }} </mat-grid-tile>
                          </div>
                        </mat-grid-list>

                        <div *ngIf="displayFreePackage">
                          <mat-grid-list cols="1" rowHeight="30px" style="
                          margin-top: 5px !important;
                          background-color: rgb(41, 41, 41) !important;
                          margin-bottom: 5px;
                        ">
                            <mat-grid-tile>Open Packages</mat-grid-tile>
                          </mat-grid-list>
                          <div *ngIf="this.openPkgUom == 'KG'; else noWeightTemplate">
                            <mat-grid-list cols="4" rowHeight="30px">
                              <mat-grid-tile>Package Name </mat-grid-tile>
                              <mat-grid-tile>Num Of Packages</mat-grid-tile>
                              <mat-grid-tile>Total Weight </mat-grid-tile>
                              <mat-grid-tile>UOM</mat-grid-tile>
                            </mat-grid-list>
                            <mat-divider [inset]="true"></mat-divider>
                            <mat-grid-list class="package_items" cols="4" rowHeight="30px">
                              <div *ngFor="let ps of packageItems; let i = index">
                                <div *ngIf="!ps.pkgName.toLowerCase().includes('case')">
                                  <mat-grid-tile>
                                    <button class="value-button" id="decrease" (click)="addOpenPkg(ps)"
                                      value="Decrease Value">+</button>

                                    Open {{ ps.pkgName }} Quantity
                                  </mat-grid-tile>
                                  <mat-grid-tile>
                                    <button class="value-button" id="decrease"
                                      (click)="ps.numOfOpenPkg = ps.numOfOpenPkg - 1" value="Decrease Value">-</button>
                                    <input matInput style="width: 80px !important;" [(ngModel)]="ps.numOfOpenPkg"
                                      min="0" type="number" step="any" class='input1'
                                      onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" />
                                    <button class="value-button" id="increase"
                                      (click)="ps.numOfOpenPkg = ps.numOfOpenPkg + 1" value="Increase Value">+</button>
                                  </mat-grid-tile>
                                  <mat-grid-tile *ngIf="this.openPkgUom == 'KG'">
                                    <button class="value-button" id="decrease"
                                      (click)="ps.otherPackages = ps.otherPackages - 1"
                                      value="Decrease Value">-</button>
                                    <input matInput style="width: 100px !important;" [(ngModel)]="ps.otherPackages"
                                      min="0" type="number" step="any" class="input1"
                                      onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" />
                                    <button class="value-button" id="increase"
                                      (click)="ps.otherPackages = ps.otherPackages + 1"
                                      value="Increase Value">+</button>
                                  </mat-grid-tile>
                                  <mat-grid-tile> {{ this.openPkgUom }} </mat-grid-tile>
                                </div>
                              </div>
                            </mat-grid-list>
                          </div>
                          <ng-template #noWeightTemplate>
                            <mat-grid-list cols="3" rowHeight="30px">
                              <mat-grid-tile>Package Name </mat-grid-tile>
                              <mat-grid-tile>Quantity</mat-grid-tile>
                              <mat-grid-tile>UOM</mat-grid-tile>
                            </mat-grid-list>
                            <mat-divider [inset]="true"></mat-divider>
                            <mat-grid-list class="package_items" cols="3" rowHeight="30px">
                              <div *ngFor="let ps of packageItems; let i = index">
                                <div *ngIf="!ps.pkgName.toLowerCase().includes('case')">
                                  <mat-grid-tile>Open {{ ps.pkgName }} Quantity</mat-grid-tile>
                                  <mat-grid-tile>
                                    <button class="value-button" id="decrease"
                                      (click)="ps.otherPackages = ps.otherPackages - 1"
                                      value="Decrease Value">-</button>
                                    <input matInput style="width: 100px !important;" [(ngModel)]="ps.otherPackages"
                                      min="0" type="number" step="any" class="input1"
                                      onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" />
                                    <button class="value-button" id="increase"
                                      (click)="ps.otherPackages = ps.otherPackages + 1"
                                      value="Increase Value">+</button>
                                  </mat-grid-tile>
                                  <mat-grid-tile> {{ this.openPkgUom }} </mat-grid-tile>
                                </div>
                              </div>
                            </mat-grid-list>
                          </ng-template>
                        </div>
                        <mat-divider [inset]="true"></mat-divider>
                        <mat-divider [inset]="true"></mat-divider>
                      </div>
                      <ng-template #noPackage>
                        <div>
                          <mat-grid-list cols="3" rowHeight="30px">
                            <mat-grid-tile>Item Name </mat-grid-tile>
                            <mat-grid-tile>Closing Stock</mat-grid-tile>
                            <mat-grid-tile>UOM</mat-grid-tile>
                          </mat-grid-list>
                          <mat-divider [inset]="true"></mat-divider>
                          <mat-grid-list class="package_items" cols="3" rowHeight="30px">
                            <div>
                              <mat-grid-tile>{{ curItem.itemName }}</mat-grid-tile>
                              <mat-grid-tile>
                                <button class="value-button" id="decrease"
                                  (click)="curItem.closingStock = curItem.closingStock - 1" value="Decrease Value">-
                                </button>
                                <div class="input-down">
                                  <input matInput class="input-box1" [(ngModel)]="curItem.closingStock" type="number" min="0"
                                    placeholder="{{ curItem.closingStock || 0 }}" step="any" (input)="getTotalIndentCostQuantity(curItem)"
                                    onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" 
                                    (focus)="focusWithOutForm(curItem,'closingStock')" (focusout)="focusOutWithOutForm(curItem,'closingStock')"/>
                                   <mat-select *ngIf="this.selectedSubCatVal === 'SUBRECIPE' && hoveredData.hasOwnProperty('portionWeight')" [(value)]="curItem.selectedOption" (selectionChange)="convertToQty(curItem)">
                                    <mat-option value="uom">UOM</mat-option>
                                    <mat-option value="portion">Portion</mat-option>
                                   </mat-select>
                                  </div>
  
                                  <button class="value-buttons" id="increase"
                                    (click)="curItem.closingStock = curItem.closingStock + 1"
                                    value="Increase Value">+</button>
                                    
                                  <div class="iconDiv" *ngIf="this.selectedSubCatVal === 'SUBRECIPE' && hoveredData.hasOwnProperty('portionWeight') && curItem.selectedOption === 'portion'">           
                                    <svg (mouseover)="onMouseOver(hoveredData)" (mouseout)="onMouseOut()" xmlns="http://www.w3.org/2000/svg" width="27" height="27" fill="currentColor" class="bi bi-calculator calIcon" viewBox="0 0 20 20">
                                      <path d="M12 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1zM4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2z"/>
                                      <path d="M4 2.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.5.5h-7a.5.5 0 0 1-.5-.5zm0 4a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm3-6a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm3-6a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5z"/>
                                    </svg>            
                                  </div>
                              </mat-grid-tile>
                              <!-- <mat-grid-tile> {{ curItem.uom }} </mat-grid-tile> -->
                              <mat-grid-tile> {{ curItem.hasOwnProperty('selectedOption') && curItem['selectedOption'] === 'portion' ? curItem.selectedOption : curItem.uom }} </mat-grid-tile>
                            </div>
                          </mat-grid-list>
                          <mat-divider [inset]="true"></mat-divider>
                          No Packaging sizes available for this item. Please enter the
                          quantity directly in closing stock coloumn.
                        </div>
                      </ng-template>
                      <button type="submit" class="btn btn-info btn-sm pull-right" (click)="saveC()">
                        Save & Continue
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <div class="custom-tooltip" [hidden]="!showTooltip">
          <div  class="parentClass">

            <div class="d-flex m-2" style="font-weight: bold; font-size: small;">
              <div class="custom-tooltip-Heading">
                Per Portion Weight
              </div>
              <div>
                {{ this.utils.truncateNew(hoveredElement?.portionWeight / 1000) }} {{ (hoveredElement?.defaultUOM) }}
              </div>
            </div>

            <div class="d-flex m-2" style="font-weight: bold; font-size: small;">
              <div class="custom-tooltip-Heading">
                Transfer Quantity
              </div>
              <div>
                {{ utils.truncateNew(hoveredElement?.convertedWeight) }} {{ (hoveredElement?.defaultUOM) }} 
              </div>
            </div>

            <div class="d-flex m-2" style="font-weight: bold; font-size: small;">
              <div class="custom-tooltip-Heading">
                No of Portion
              </div>
              <div>
                {{ this.utils.truncateNew(hoveredElement?.closingStock) }}
              </div>
            </div>
          </div>

        </div>
      </mat-tab>

      <mat-tab *ngIf="!this.router.url.includes(this.transferClosingUrl)">
        <ng-template mat-tab-label>Status</ng-template>
        <br>

          <div *ngIf="!showItems" class="statusInputDiv">
            <form [formGroup]="closingForm" class="topHeadInputs">
              <mat-form-field *ngIf="multiBranchUser" appearance="none" class="mt-1 gap">
                <label>Select Branch</label>
                <mat-select placeholder="Select Branch" class="outline" formControlName="branchSelection"
                  [(ngModel)]='this.currentBranchName' (selectionChange)="filterByBranch($event.value)">
                  <mat-option *ngFor="let rest of branches" [value]="rest">
                    {{ rest.branchName }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
              <mat-form-field appearance="none" *ngIf="branchSelected" class="mt-1 gap">
                <label>Select Work Area</label>
                <mat-select placeholder="Select Work Area" formControlName="workAreaSelection"
                  (selectionChange)="selectIndentArea($event.value)" class="outline">
                  <mat-option>
                    <ngx-mat-select-search placeholderLabel="Work Area..." noEntriesFoundLabel="'Work Area Not Found'"
                      [formControl]="vendorFilterCtrl"></ngx-mat-select-search>
                  </mat-option>
                  <mat-option *ngFor="let area of vendorsBanks | async" [value]="area">
                    {{ area }}
                  </mat-option>
                </mat-select>
              </mat-form-field>

            </form>
            <form [formGroup]="jobs" class="topHeadInputs">

              <mat-form-field appearance="none" class="mt-1 gap">
                <label>Source Type</label>
                <mat-select placeholder="Select" formControlName="sourceType" class="outline"
                  (selectionChange)="selectedSource($event.value)">
                  <mat-option *ngFor="let val of ['UI' , 'Excel']" [value]="val">
                    {{ val }}
                  </mat-option>
                </mat-select>
              </mat-form-field>

              <mat-form-field appearance="none" class="mt-1 gap">
                <label>Select</label>
                <mat-select placeholder="Select" formControlName="jobs" class="outline" (selectionChange)="getJobs()">
                  <mat-option *ngFor="let category of events" [value]="category"
                    [disabled]="category.displayValue === 'Generated Templates' && this.jobs.value.sourceType === 'UI'">
                    {{category.displayValue}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </form>
            <button mat-button (click)="refreshtable()"
              class="refreshBtnclosing buttonForRefresh mb-2">Refresh</button>
          </div>
          <div>
            <table mat-table [dataSource]="jobsDataSource">
              <ng-container matColumnDef="templateId" *ngIf="jobs.value.jobs.value == 'template'">
                <th mat-header-cell *matHeaderCellDef> templateId </th>
                <td mat-cell *matCellDef="let element"> {{element.templateNo}} </td>
              </ng-container>
              <ng-container matColumnDef="templateId" *ngIf="jobs.value.jobs.value == 'excelStockClosing'">
                <th mat-header-cell *matHeaderCellDef> templateId </th>
                <td mat-cell *matCellDef="let element"> {{element.details.templateNo || element.reportNo }} </td>
              </ng-container>
              <ng-container matColumnDef="email">
                <th mat-header-cell *matHeaderCellDef> User </th>
                <td mat-cell *matCellDef="let element"> {{element.details.email.split('@')[0]}} </td>
              </ng-container>
              <ng-container matColumnDef="type" *ngIf="jobs.value.jobs.value == 'template'">
                <th mat-header-cell *matHeaderCellDef> Type </th>
                <td mat-cell *matCellDef="let element"> {{element.details.type}} </td>
              </ng-container>
              <ng-container matColumnDef="type" *ngIf="jobs.value.jobs.value == 'excelStockClosing'">
                <th mat-header-cell *matHeaderCellDef> Type </th>
                <td mat-cell *matCellDef="let element"> {{element.details.closingType || element.details.type}} </td>
              </ng-container>
              <ng-container matColumnDef="indentArea">
                <th mat-header-cell *matHeaderCellDef> IndentArea </th>
                <td mat-cell *matCellDef="let element"> {{element.details.indentArea ||
                  element.details.selectedWorkAreas}} </td>
              </ng-container>
              <ng-container matColumnDef="closingDate">
                <th mat-header-cell *matHeaderCellDef> Closing Date </th>
                <td mat-cell *matCellDef="let element"> {{element.details.date || element.details.requestDate |
                  date:'MMM d' }} </td>
              </ng-container>
              <ng-container matColumnDef="createTs">
                <th mat-header-cell *matHeaderCellDef> Uploaded Date </th>
                <td mat-cell *matCellDef="let element"> {{ this.utils.formatDateToUTC(element.createTs)}} </td>
              </ng-container>
              <ng-container matColumnDef="generateTs">
                <th mat-header-cell *matHeaderCellDef> Requested Date </th>
                <td mat-cell *matCellDef="let element"> {{ this.utils.formatDateToUTC(element.createTs)}} </td>
              </ng-container>
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef> Status </th>
                <td mat-cell *matCellDef="let element">
                  <div *ngIf="element.pssi===true">Completed
                    <mat-icon class="check_circle">check_circle</mat-icon>
                  </div>
                  <div *ngIf="element.pssi===false">On Progress
                    <div class="spinner-border" role="status">
                      <span class="sr-only">Loading...</span>
                    </div>
                  </div>
                </td>
              </ng-container>

              <ng-container matColumnDef="download">
                <th mat-header-cell *matHeaderCellDef> Export </th>
                <td mat-cell *matCellDef="let element">
                  <mat-icon matTooltip="Download" *ngIf="this.jobs.value.sourceType != 'UI'"
                    (click)="downloadTemplates(element)">download</mat-icon>
                  <mat-icon matTooltip="Download" *ngIf="this.jobs.value.sourceType === 'UI'"
                    (click)="download(element)">download</mat-icon>
                </td>
              </ng-container>

              <ng-container matColumnDef="error">
                <th mat-header-cell *matHeaderCellDef> Error </th>
                <td mat-cell *matCellDef="let element">
                  <div *ngIf="jobs.value.jobs.value == 'template'">
                    <div *ngIf="element.error">Error while Generating</div>
                    <div *ngIf="!element.error">-</div>
                  </div>
                  <div *ngIf="jobs.value.jobs.value == 'excelStockClosing'">
                    <div *ngIf="element.error">

                      <span (click)="getErrorLog(element)" matTooltip="click to to view error">
                        Error while Processing
                        <mat-icon>preview</mat-icon>
                      </span>
                    </div>
                    <div *ngIf="!element.error">-</div>
                  </div>
                </td>
              </ng-container>
              <tr mat-header-row *matHeaderRowDef="jobsDisplayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: jobsDisplayedColumns;"></tr>
            </table>
          </div>
          <div class="dataMessage" *ngIf="jobsDataSource?.data?.length == 0"> No Data Available </div>
      </mat-tab>
    </mat-tab-group>
  </mat-card>
</div>

<div class="closingContainer" *ngIf='finalSubmit'>
  <div class="closingContainerDatas">
    <mat-card class="matCardForClosing">
      <div class="closeMsg">
        {{finalMsg}}
      </div>
      <div class="closeMsgBtn mt-2">
        <button mat-button mat-raised-button id="save-btn" class="button2 button3" (click)="redirect()">Click
          Here</button>
      </div>

      <div *ngFor="let item of missItemList" style="margin-left: 20px;">
        <span>{{item}}</span>
      </div>
    </mat-card>
  </div>
</div>


<ng-template #openExcelError>
  <button mat-icon-button matDialogClose="yes" matTooltip="Close" class="CloseBtn">
    <mat-icon>close</mat-icon>
  </button>
  <h2 mat-dialog-title>
    <b>Excel Upload Error!</b>
  </h2>

  <mat-dialog-content class="mat-typography">
    <div class="m-3" *ngFor="let data of errorData; let i = index;">
      <div style="font-size: larger; margin: 5px;">
        <b class="d-flex align-items-center">
          {{data?.sheetName | titlecase}} Sheet
          <mat-icon class="checkIcon errorCheckIcon" *ngIf="data.missingColumns.length === 0">check_circle</mat-icon>
          <mat-icon class="cancelIcon errorCheckIcon" *ngIf="data.missingColumns.length > 0">cancel</mat-icon>
        </b>
      </div>
      <div class="m-2">
        <div *ngFor="let value of data.missingColumns; let i = index;" class="errorData">
          {{i + 1 }} . {{value}} *
        </div>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align='center' *ngIf="!dialogLoader">
    <div class="bottomTitles errormsg p-2 my-3 d-flex flex-wrap align-items-center">
      * There are missing columns in your uploaded sheet
    </div>
  </mat-dialog-actions>
</ng-template>


<ng-template #openExportDialog>
  <div class="d-flex flex-wrap align-items-center justify-content-end">
    <button mat-icon-button matDialogClose="yes" matTooltip="Close">
      <mat-icon>close</mat-icon>
    </button>
  </div>
  <mat-dialog-content class="mat-typography">
    <div class="checkBoxClass mt-3 mr-4 ml-4 mb-2">
      <span class="dialogMsg">Doing a bar count? select the checkbox if you have several open bottles to track ? </span>
    </div>
    <div class="d-flex justify-content-center mx-1 mb-2 mt-3">
      <mat-checkbox [(ngModel)]="isStore">Bar Closing</mat-checkbox>
    </div>
  </mat-dialog-content>
  <mat-dialog-actions align='center'>
    <div class="m-2 d-flex flex-wrap align-items-center justify-content-end">
      <button mat-stroked-button class="button3" (click)="generateTemplate()">
        Generate Now
      </button>
    </div>
  </mat-dialog-actions>
</ng-template>
