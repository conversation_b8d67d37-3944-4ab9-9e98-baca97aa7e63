import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { GlobalsService } from '../_services/globals.service';
import { HttpClient, HttpParams } from '@angular/common/http';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class BranchTransferService {
  baseUrl: string = environment.baseUrl;

  constructor(private http: HttpClient) { }
  createIbt(obj) {
    return this.http.post(`${this.baseUrl}createIbt`, obj).pipe(map((res: any) => res))
  }



  getBranchInv(obj) {
    let params = {}
    if(obj.hasOwnProperty('isStoreClosing')){
      params = new HttpParams()
     .set(GlobalsService.tenantId, obj.tenantId)
     .set(GlobalsService.restaurantId, obj.restaurantId).set('reqQty',obj.reqQty)
     .set('isClosing',obj.isClosing).set('userEmail',obj.userEmail)
     .set('isStoreClosing',obj.isStoreClosing)
    }
    else if(obj.hasOwnProperty('isKitchenClosing')){
      params = new HttpParams()
     .set(GlobalsService.tenantId, obj.tenantId)
     .set(GlobalsService.restaurantId, obj.restaurantId).set('reqQty',obj.reqQty)
     .set('isClosing',obj.isClosing).set('userEmail',obj.userEmail)
     .set('isKitchenClosing',obj.isKitchenClosing)
    }
    else if(obj.hasOwnProperty('specialFlag')){
      params = new HttpParams()
     .set(GlobalsService.tenantId, obj.tenantId)
     .set(GlobalsService.restaurantId, obj.restaurantId).set('reqQty',obj.reqQty)
     .set('specialFlag',obj.specialFlag).set('userEmail',obj.userEmail).set('workArea',obj.workArea)
    }
    else{
      params = new HttpParams()
     .set(GlobalsService.tenantId, obj.tenantId)
     .set(GlobalsService.restaurantId, obj.restaurantId).set('reqQty',obj.reqQty)
     return null
    }
    return this.http.get(`${this.baseUrl}getBranchInv`, { params: params }).pipe(map((res: any) => {
      if (res.result === 'success') {
        res.invItems = res.invItems.sort((a, b) => a.itemCode > b.itemCode)
        res.invItems = res.invItems.filter(item => item.closingUom != 'NA')
        return res
      }
      return null
    }))
  }

  getBranchInvNew(obj) {
    let params = {}
    if(obj.hasOwnProperty('isStoreClosing')){
      params = new HttpParams()
     .set(GlobalsService.tenantId, obj.tenantId)
     .set(GlobalsService.restaurantId, obj.restaurantId).set('reqQty',obj.reqQty)
     .set('isClosing',obj.isClosing).set('userEmail',obj.userEmail)
     .set('transfer',obj.transfer).set('userEmail',obj.userEmail)
     .set('isStoreClosing',obj.isStoreClosing).set('workArea',obj.workArea)
     .set('category',obj.category)
     
    }
    else if(obj.hasOwnProperty('isKitchenClosing')){
      params = new HttpParams()
     .set(GlobalsService.tenantId, obj.tenantId)
     .set(GlobalsService.restaurantId, obj.restaurantId).set('reqQty',obj.reqQty)
     .set('isClosing',obj.isClosing).set('userEmail',obj.userEmail)
     .set('isKitchenClosing',obj.isKitchenClosing).set('workArea',obj.workArea)
     .set('category',obj.category)

    }
    else if(obj.hasOwnProperty('specialFlag')){
      params = new HttpParams()
     .set(GlobalsService.tenantId, obj.tenantId)
     .set(GlobalsService.restaurantId, obj.restaurantId).set('reqQty',obj.reqQty)
     .set('specialFlag',obj.specialFlag).set('userEmail',obj.userEmail).set('workArea',obj.workArea)
    }
    else{
      params = new HttpParams()
      .set(GlobalsService.tenantId, obj.tenantId)
      .set(GlobalsService.restaurantId, obj.restaurantId).set('reqQty',obj.reqQty)
      return null
    }
    return this.http.get(`${this.baseUrl}getBranchInv`, { params: params }).pipe(map((res: any) => {
      if (res.result === 'success') {
        res.invItems = res.invItems.sort((a, b) => a.itemCode > b.itemCode)
        res.invItems = res.invItems.filter(item => item.closingUom != 'NA')
        return res
      }
      return null
    }))
  }

  getSpecialIndentData(obj) {
    let params = {}
    if(obj.hasOwnProperty('isStoreClosing')){
      params = new HttpParams()
     .set(GlobalsService.tenantId, obj.tenantId)
     .set(GlobalsService.restaurantId, obj.restaurantId).set('reqQty',obj.reqQty)
     .set('isClosing',obj.isClosing).set('userEmail',obj.userEmail)
     .set('isStoreClosing',obj.isStoreClosing).set('workArea',obj.workArea)
    }
    else if(obj.hasOwnProperty('isKitchenClosing')){
      params = new HttpParams()
     .set(GlobalsService.tenantId, obj.tenantId)
     .set(GlobalsService.restaurantId, obj.restaurantId).set('reqQty',obj.reqQty)
     .set('isClosing',obj.isClosing).set('userEmail',obj.userEmail)
     .set('isKitchenClosing',obj.isKitchenClosing).set('workArea',obj.workArea)
    }
    else if(obj.hasOwnProperty('specialFlag')){
      params = new HttpParams()
     .set(GlobalsService.tenantId, obj.tenantId)
     .set(GlobalsService.restaurantId, obj.restaurantId)
     .set(GlobalsService.destinationId, obj.destinationId)
     .set('reqQty',obj.reqQty)
     .set('specialFlag',obj.specialFlag).set('userEmail',obj.userEmail)
     .set('workArea',obj.workArea)
     if (obj.hasOwnProperty('stockType')){
      params = new HttpParams()
     .set(GlobalsService.tenantId, obj.tenantId)
     .set(GlobalsService.restaurantId, obj.restaurantId)
     .set(GlobalsService.destinationId, obj.destinationId)
     .set('reqQty',obj.reqQty)
     .set('specialFlag',obj.specialFlag).set('userEmail',obj.userEmail).set('workArea',obj.workArea)
     .set('stockType',obj.stockType)
     .set('category',obj.category.join(','))
     }
      
    }
    else{
      params = new HttpParams()
     .set(GlobalsService.tenantId, obj.tenantId)
     .set(GlobalsService.restaurantId, obj.restaurantId).set('reqQty',obj.reqQty)
    }
    
    return this.http.get(`${this.baseUrl}getSpecialIndentData`, { params: params }).pipe(map((res: any) => {
      if (res.result === 'success') {
        res.invItems = res.invItems.sort((a, b) => a.itemCode > b.itemCode)
        res.invItems = res.invItems.filter(item => item.closingUom != 'NA')
        return res
      }
      return null
    }))
  }

  getIbts(obj) {
    return this.http.post(`${this.baseUrl}getIbts`, obj).pipe(map((res: any) => {
      if (res.result === 'success') {
        return res.ibts
      }
      return null

    }))
  }

  getInStockForIbt(obj){
    return this.http.post(`${this.baseUrl}getInStockIbt`, obj).pipe(map((res: any) => res))
  }

  deleteIndentList(obj){
    return this.http.post(`${this.baseUrl}deleteIndentList`, obj).pipe(map((res: any) => res))
  }

  deleteIndent(obj){
    return this.http.post(`${this.baseUrl}deleteIndent`, obj).pipe(map((res: any) => res))
  }

  deleteIbt(obj){
    return this.http.post(`${this.baseUrl}deleteIbt`, obj).pipe(map((res: any) => res))
  }

  generateGrnFromIbt(obj){
      return this.http.post(`${this.baseUrl}createGrnForIbt`, obj).pipe(map((res: any) => res))
  }


  updateIbt(obj){
    return this.http.post(`${this.baseUrl}updateIbt`, obj).pipe(map((res: any) =>  res))
  }

  postIntraBranchTransfer(obj){
    return this.http.post(`${this.baseUrl}postIntraBranchTransfer`, obj).pipe(map((res: any) =>  res))
  }

  getBranch(obj){
    return this.http.post(`${this.baseUrl}getBranch`, obj).pipe(map((res: any) =>  res))
  }

  voidIbt(obj){
      return this.http.post(`${this.baseUrl}voidIbt`, obj).pipe(map((res: any) =>  res))
  }

  issueIndent(obj){
    return this.http.post(`${this.baseUrl}issue`, obj).pipe(map((res: any) =>  res))
  }

  reverseIbt(obj){
    return this.http.post(`${this.baseUrl}reverseIbt`, obj).pipe(map((res: any) =>  res))

  }
  printpdfs(obj, type){
    return this.http.post(`${this.baseUrl}print`+type, obj).pipe(map((res: any) => {
      if (res.result === 'success')
        return res
      }))
  }

  exportToExcel(obj, type){
    return this.http.post(`${this.baseUrl}export`+type, obj).pipe(map((res: any) => {
      if (res.result === 'success')
        return res
      }))
  }

  saveIndentReq(obj){
    return this.http.post(`${this.baseUrl}saveIndentReq`, obj).pipe(map((res: any) =>  {
      return res
    }))
  }

  issueIndentReq(obj){
    return this.http.post(`${this.baseUrl}issueIndentReq`, obj).pipe(map((res: any) =>  {
      return res
    }))
  }

  issueIndentRequest(obj){
    return this.http.post(`${this.baseUrl}issueIndentRequest`, obj).pipe(map((res: any) =>  {
      return res
    }))
  }

  tenantDetails(obj){
    return this.http.post(`${this.baseUrl}getTenantData`, obj).pipe(map((res: any) =>  {
      return res
    }))
  }

  cloneIndent(obj){
    return this.http.post(`${this.baseUrl}cloneIndent`, obj).pipe(map((res: any) =>  {
      return res
    }))
  }

  getIndentList(obj){
    return this.http.post(`${this.baseUrl}getIndentList`, obj).pipe(map((res: any) => {
      if (res.result === 'success') {
        return res.indentList
      }
      return null
    }));
  }

  // getIndentRequestList(obj){
  //   return this.http.post(`${this.baseUrl}getIndentRequest`, obj).pipe(map((res: any) => {
  //     if (res.result === 'success') {
  //       return res.indentList
  //     }
  //     return null
  //   }));
  // }

  getIndentRequestList(obj) {
    let params = new HttpParams()
      .set(GlobalsService.tenantId, obj.tenantId)
      .set(GlobalsService.restaurantId, obj.restaurantId)
      .set(GlobalsService.startDate, obj.startDate)
      .set(GlobalsService.endDate, obj.endDate)
      .set(GlobalsService.userEmail, obj.userEmail)
      .set(GlobalsService.indentWorkArea, obj.workArea)
      // .set(GlobalsService.indentApprovalStatus, obj.approvalStatus)
    return this.http.get(`${this.baseUrl}getIndentRequest`, { params: params }).pipe(map((res: any) => {
      if (res.result === 'success') {
        return res.indentList
      }
      return null
    }))
  }

  // getCsIndentRequestList(obj){
  //   return this.http.post(`${this.baseUrl}getCentralIndentRequest`, obj).pipe(map((res: any) => {
  //     if (res.result === 'success') {
  //       return res.indentList
  //     }
  //     return null
  //   }));
  // }

  getCsIndentRequestList(obj) {
    let params = new HttpParams()
      .set(GlobalsService.tenantId, obj.tenantId)
      .set(GlobalsService.restaurantId, obj.restaurantId)
      .set(GlobalsService.startDate, obj.startDate)
      .set(GlobalsService.endDate, obj.endDate)
      .set(GlobalsService.userEmail, obj.userEmail)
      .set(GlobalsService.csiIndentOrderStatus, obj.orderStatus)
      // .set(GlobalsService.csiIndentApprovalStatus, obj.approvalStatus)
    return this.http.get(`${this.baseUrl}getCentralIndentRequest`, { params: params }).pipe(map((res: any) => {
      if (res.result === 'success') {
        return res.indentList
      }
      return null
    }))
  }

  getPIList(obj) {
    let params = new HttpParams()
      .set(GlobalsService.tenantId, obj.tenantId)
      .set(GlobalsService.restaurantId, obj.restaurantId)
      .set(GlobalsService.startDate, obj.startDate)
      .set(GlobalsService.endDate, obj.endDate)
      .set(GlobalsService.userEmail, obj.userEmail)
    return this.http.get(`${this.baseUrl}getPIList`, { params: params }).pipe(map((res: any) => {
      if (res.success) {
        return res.data
      }
      return null
    }))
  }

  getCurrentStockForIndent(obj){
    return this.http.post(`${this.baseUrl}getCurrentStockForIndent`, obj).pipe(map((res: any) => {
      if (res.result === 'success') {
        return res.indentItems
      }
      return null
    }));

  }

  dispatchIndent(obj){
    return this.http.post(`${this.baseUrl}dispatchIndent`, obj).pipe(map((res: any) => {
      return res
      
    }));

  }

  getPkgStockVal(obj){
    return this.http.post(`${this.baseUrl}getPkgStockVal`, obj).pipe(map((res: any) => {
      if (res.result === 'success') {
        res.invItems = res.invItems.sort((a, b) => a.itemCode > b.itemCode)
        return res
      }
      return null
    }))
  }

  
  getWorkAreasForBranch(obj){
    return this.http.post(`${this.baseUrl}getWorkAreasForBranch`, obj).pipe(map((res: any) => {
      if (res.result === 'success') {
        return res
      }
      return null
    }));
  }

  getWorkAreaInv(obj){
    return this.http.post(`${this.baseUrl}getWorkAreaInv`, obj).pipe(map((res: any) => {
      if (res['success']) {
        res.invItems = res.invItems.sort((a, b) => a.itemCode > b.itemCode)
        return res
      }else{
        return res
      }
      
    }));
  }

  returnToStore(obj){
    return this.http.post(`${this.baseUrl}returnToStore`, obj).pipe(map((res: any) => {
      return res;
    }));
  }

  getIbtInvWithPkg(obj){
    return this.http.post(`${this.baseUrl}getIbtInvWithPkg`, obj).pipe(map((res: any) => {
      if (res.result === 'success') {
        res.invItems = res.invItems.sort((a, b) => a.itemCode > b.itemCode)
        return res
      }
      return null
      
    }));
  }

  getIbtInvWithWorkArea(obj){
    return this.http.post(`${this.baseUrl}getIbtInvWithWorkArea`, obj).pipe(map((res: any) => {
      if (res.result === 'success') {
        res.invItems = res.invItems.sort((a, b) => a.itemCode > b.itemCode)
        return res
      }
      return null
      
    }));
  }

  getIndentPrediction(obj){
      return this.http.post(`${this.baseUrl}getIndentPrediction`,obj).pipe(map((res: any) => {
          return res;
      }));
  }

  getCategories(obj){
    return this.http.post(`${this.baseUrl}getAllStockCategory`,obj).pipe(map((res: any) => {
        return res;
    }));
}

  getItemType(obj){
    return this.http.post(`${this.baseUrl}getAllItemType`,obj).pipe(map((res: any) => {
        return res;
    }));
  }


  getRtsInv(obj){
    return this.http.post(`${this.baseUrl}getRtsInv`,obj).pipe(map((res: any) => {
      if (res.result === 'success') {
        res.invItems = res.invItems.sort((a, b) => a.itemName > b.itemName)
        return res
      }
  }));

  }

  rtsReq(obj){
    return this.http.post(`${this.baseUrl}rtsReq`, obj).pipe(map((res: any) =>  {
      return res
    }))
  }

  getRtsList(obj){
    return this.http.post(`${this.baseUrl}getRtsList`, obj).pipe(map((res: any) =>  {
      if (res.result === 'success') {
        return res.rtsList
      }
      return null
    }))
  }

  acceptReturnItem(obj){
    return this.http.post(`${this.baseUrl}acceptReturnItem`, obj).pipe(map((res: any) => {
      return res
      
    }));
  }

  getAdjustInventoryData(obj){
    return this.http.post(`${this.baseUrl}getRtsInv`,obj).pipe(map((res: any) => {
      if (res.result === 'success') {
        res.invItems = res.invItems.sort((a, b) => a.itemName > b.itemName)
        return res
      }
    }));
  }

  getSpoilageInventoryData(obj){
    return this.http.post(`${this.baseUrl}getSpoilageInv`,obj).pipe(map((res: any) => {
      if (res.result === 'success') {
        res.invItems = res.invItems.sort((a, b) => a.itemName > b.itemName)
        return res
      }
    }));
  }

  createAdjustReq(obj){
    return this.http.post(`${this.baseUrl}createAdjustReq`,obj).pipe(map((res: any) => {
      return res;
    }));
  }

  updateSpoilage(obj){
    return this.http.post(`${this.baseUrl}updateSpoilage`,obj).pipe(map((res: any) => {
      return res;
    }));
  }

  getSpoilageList(obj){
    return this.http.post(`${this.baseUrl}getSpoilageList`,obj).pipe(map((res: any) => {
      return res;
    }));
  }

  getStockData(obj){
    return this.http.post(`${this.baseUrl}getStockData`,obj).pipe(map((res: any) => {
      return res;
    })); 
  }

  reduceSpoilage(obj){
    return this.http.post(`${this.baseUrl}reduceSpoilage`,obj).pipe(map((res: any) => {
      return res;
    })); 
  }

  saveSpoilage(obj){
    return this.http.post(`${this.baseUrl}saveSpoilage`,obj).pipe(map((res: any) => {
      return res;
    })); 
  }

  getAdjInvList(obj){
    return this.http.post(`${this.baseUrl}getAdjInvList`, obj).pipe(map((res: any) =>  {
      if (res.result === 'success') {
        return res.rtsList
      }
      return null
    }))
  }

  approveAdjustInvItem(obj){
    return this.http.post(`${this.baseUrl}approveAdjustInvItem`, obj).pipe(map((res: any) => {
      return res
      
    }));
  }

}
