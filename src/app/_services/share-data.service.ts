import { Injectable } from '@angular/core';
import { PurchaseOrder } from '../_models';
import { BehaviorSubject } from 'rxjs';
@Injectable({
  providedIn: 'root'
})
export class ShareDataService {
  private purReq: any;

  public getBranch = new BehaviorSubject<any>([]);
  sharedBranchData = this.getBranch.asObservable();

  public setTimeOutData = new BehaviorSubject<any>([]);
  getTimeOutData = this.setTimeOutData.asObservable();

  private purOrder = new BehaviorSubject<PurchaseOrder>({});
  currPurOrder = this.purOrder.asObservable();
  private indents = new BehaviorSubject<PurchaseOrder>({});
  indentsDetails = this.indents.asObservable();
  private pr = new BehaviorSubject<any>({});
  currPR = this.pr.asObservable();
  private grn = new BehaviorSubject<any>({});
  currGrn = this.grn.asObservable();
  private ledger = new BehaviorSubject<any>({});
  currLedger = this.ledger.asObservable();
  private pi = new BehaviorSubject<any>({});
  currPi = this.pi.asObservable();  
  private rtv = new BehaviorSubject<any>({});
  currRtv = this.rtv.asObservable();
  private rtvs = new BehaviorSubject<any>({});
  currRtvs = this.rtvs.asObservable();
  private ibts = new BehaviorSubject<any>({});
  currIbts = this.ibts.asObservable();
  private ibt = new BehaviorSubject<any>({});
  currIbt = this.ibt.asObservable();
  private indent = new BehaviorSubject<any>([]);
  currIndent = this.indent.asObservable();
  private rts = new BehaviorSubject<any>([]);
  currRts = this.rts.asObservable();
  private adjInv = new BehaviorSubject<any>([]);
  currAdjInv = this.adjInv.asObservable();
  private prTmp = new BehaviorSubject<any>([]);
  currPrTmp = this.prTmp.asObservable();
  private currForcastRes = new BehaviorSubject<any>([]);
  ForcastRes = this.currForcastRes.asObservable();

  private pl = new BehaviorSubject<any>({});
  priceListData = this.pl.asObservable();

  private sc = new BehaviorSubject<any>({});
  stockConversionData = this.sc.asObservable();

  private scl = new BehaviorSubject<any>({});
  stockConversionListData = this.scl.asObservable();


  private dscl = new BehaviorSubject<any>({});
  detailedStockConversionListData = this.dscl.asObservable();
  
  
  private cr = new BehaviorSubject<any>({});
  crData = this.cr.asObservable();

  constructor() { }
  changeOrder(obj) {
    this.purOrder.next(obj);
  }
  changeIndentDetails(obj) {
    this.indents.next(obj);
  }
  changePr(obj) {
    this.pr.next(obj);
  }
  changeGrn(obj) {
    this.grn.next(obj);
  }
  editPr(value) {
    this.purReq = value;
  }
  getEditPr() {
    return this.purReq;
  }
  clearPrData() {
    this.purReq = null;
  }

  changeLedger(obj){
    this.ledger.next(obj);
  }

  changePi(obj) {
    this.pi.next(obj);
  }  
  changeRtv(obj) {
    this.rtv.next(obj);
  }
  changeRtvs(obj) {
    this.rtvs.next(obj);
  }
  changeIntraBranch(obj) {
    this.ibts.next(obj);
  }
  changeIbt(obj) {
    this.ibt.next(obj);
  }
  changeIndent(obj,indentReqFlag){
    this.indent.next([obj,indentReqFlag]);
  }
  changeRts(obj,rtsWorkAreaViewFlag){
    this.rts.next([obj,rtsWorkAreaViewFlag]);
  }

  changeAdjInv(obj){
    this.adjInv.next(obj);
  }

  changePrTmp(obj){
    this.prTmp.next(obj);
  }

  changeForcastRes(obj){
    this.currForcastRes.next(obj);
  }

  pricelist(obj){
    this.pl.next(obj);
  }

  stockConversion(obj){
    this.sc.next(obj);
  }

  stockConversionList(obj){
    this.scl.next(obj);
  }

  DetailedStockConversionList(obj){
    this.dscl.next(obj);
  }

  createReq(obj){
    this.cr.next(obj);
  }

  sharedBranchArr(obj){
    this.getBranch.next(obj)
  }

  sendTimeOutData(data){
    this.setTimeOutData.next(data)
  }

}
