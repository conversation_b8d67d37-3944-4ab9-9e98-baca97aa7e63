import { BrowserModule } from "@angular/platform-browser";
import { NgModule,LOCALE_ID, CUSTOM_ELEMENTS_SCHEMA } from "@angular/core";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { MatComponentsModule } from "./mat-components.module";
import { AppRoutingModule } from "./app-routing.module";
import { AppComponent } from "./app.component";
import { LoginComponent } from "./login/login.component";
import { ReactiveFormsModule, FormsModule } from "@angular/forms";
import { LoginLayoutComponent } from "./login-layout/login-layout.component";
import { HomeLayoutComponent } from "./home-layout/home-layout.component";
import { NavigationComponent } from "./navigation/navigation.component";
import { HomeComponent } from "./home/<USER>";
import { AuthService } from "./_services/auth.service";
import { MenuItemService } from "./_services/menu-item.service";
import { HttpClientModule, HTTP_INTERCEPTORS } from "@angular/common/http";
import { AuthGuard } from "./_guards/";
import { LoginGuard } from "./_guards/";
import { ProductionPlanningComponent } from "./production-planning/production-planning.component";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { MatCardModule, MAT_DATE_LOCALE } from '@angular/material';
import { TableParamsComponent } from "./table-params/table-params.component";
import { AuthInterceptor } from "./_interceptors/auth.interceptor";
import { Ng2SmartTableModule } from "ng2-smart-table";
import { DigiTableComponent } from "./digi-table/digi-table.component";
import { UtilsService } from "./_utils/utils.service";
import { ForecastReportComponent } from "./forecast-report/forecast-report.component";
import { NgxDaterangepickerMd } from "ngx-daterangepicker-material";
import { LoadingScreenComponent } from "./loading-screen/loading-screen.component";
import { LoadingScreenInterceptor } from "./_interceptors/loading.interceptor";
import { LoadingScreenService } from "./_services/";
import { ErrorDialogComponent } from "./error-dialog/error-dialog.component";
import { PageNotFoundComponent } from "./page-not-found/page-not-found.component";
import { PurchaseListComponent } from "./purchase-list/purchase-list.component";
import { FlexLayoutModule } from "@angular/flex-layout";
import { OrderTableComponent } from "./order-table/order-table.component";
import { VendorPuchaseRequestsComponent } from "./vendor-puchase-requests/vendor-puchase-requests.component";
import { PurchaseOrdersComponent } from "./purchase-orders/purchase-orders.component";
import { VendorPurchaseOrdersComponent } from "./vendor-purchase-orders/vendor-purchase-orders.component";
import { PurchaseOrderTableComponent } from "./purchase-order-table/purchase-order-table.component";
import { CreatePurchaseOrderComponent } from "./create-purchase-order/create-purchase-order.component";
import { SimpleDialogComponent } from "./_dialogs/simple-dialog/simple-dialog.component";
import { InvoiceComponent } from "./invoice/invoice.component";
import { ReceiveOrderComponent } from "./receive-order/receive-order.component";
import { InvoiceDialogComponent } from "./_dialogs/invoice-dialog/invoice-dialog.component";
import { PrPreviewDialogComponent } from "./_dialogs/pr-preview-dialog/pr-preview-dialog.component";
import { PurchaseRequestsComponent } from "./purchase-requests/purchase-requests.component";
import { DetailedPrComponent } from "./detailed-pr/detailed-pr.component";
import { GrnComponent } from "./grn/grn.component";
import { DetailedGrnComponent } from "./detailed-grn/detailed-grn.component";
import { IbtComponent } from "./ibt/ibt.component";
import { IbtsComponent } from "./ibts/ibts.component";
import { DetailedIbtComponent } from "./detailed-ibt/detailed-ibt.component";
import { PreviewIbtComponent } from "./_dialogs/preview-ibt/preview-ibt.component";
import { InventoryListComponent } from "./invenotry-list/invenotry-list.component";
import { IndentsComponent } from "./indents/indents.component";
import { IssueIndentComponent } from "./issue-indent/issue-indent.component";
import { PackageDialogComponent } from "./_dialogs/package-dialog/package-dialog.component";
import { Ng2SearchPipeModule } from "ng2-search-filter";
import { DigitoryPaginatorDirective } from "./digitory-paginator.directive";
import { AdjustInventoryComponent } from './adjust-inventory/adjust-inventory.component';
import { IndentsListComponent } from './indents-list/indents-list.component';
import { IndentsDetailComponent } from './indents-detail/indents-detail.component';
import { IndentPredictionComponent } from './indent-prediction/indent-prediction.component';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { InitiateRtvComponent } from './initiate-rtv/initiate-rtv.component';
import { RtvListComponent } from './rtv-list/rtv-list.component';
import { DetailedRtvComponent } from './detailed-rtv/detailed-rtv.component';
import { InitiateRtsComponent } from './initiate-rts/initiate-rts.component';
import { RtsListComponent } from './rts-list/rts-list.component';
import { RtsDetailComponent } from './rts-detail/rts-detail.component';
import { AdjustInvListComponent } from './adjust-inv-list/adjust-inv-list.component';
import { AdjustInvDetailComponent } from './adjust-inv-detail/adjust-inv-detail.component';
import { PurchaseSettingComponent } from './purchase-setting/purchase-setting.component';
import { PurchaseApprovalComponent } from './purchase-approval/purchase-approval.component';
import { PoapprovalDetailComponent } from './poapproval-detail/poapproval-detail.component';
import { InfoComponent } from "./_dialogs/info/info.component";
import { PythonScriptComponent } from './python-script/python-script.component';
import { MatAutocompleteModule, MatBadgeModule, MatButtonModule, MatDialogModule, MatFormFieldModule, MatIconModule, MatInputModule, MatListModule, MatSelectModule, MatSidenavModule, MatSliderModule, MatTabsModule, MatToolbarModule, MatTooltipModule } from "@angular/material";
import { MatDatetimepickerModule, MatNativeDatetimeModule } from "@mat-datetimepicker/core";
import { ToastrModule } from 'ngx-toastr';
import { MatStepperModule } from '@angular/material';
import { IntraBranchComponent } from './intra-branch/intra-branch.component';
import { PurchaseInvoiceComponent } from './purchase-invoice/purchase-invoice.component';
import { DetailedPiComponent } from './detailed-pi/detailed-pi.component';
import { CustomCarouselComponent } from './custom-carousel/custom-carousel.component';
import { JobmonitorComponent } from "./job-monitor/job-monitor.component";
import { CrmQrComponent } from './crm-qr/crm-qr.component';
import { EmailStatusComponent } from './email-status/email-status.component';
import { ClosingComponent } from './closing/closing.component';
import { NotifiDialogComponent } from './_dialogs/notifi-dialog/notifi-dialog.component';
import { PriceListComponent } from './price-list/price-list.component';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { ImageViewerModule } from 'ng2-image-viewer';
import { DetailedPlComponent } from './detailed-pl/detailed-pl.component';
import { A11yModule } from '@angular/cdk/a11y';
import { PoApprovalComponent } from './po-approval/po-approval.component';
import { GrnapprovalDetailComponent } from './grnapproval-detail/grnapproval-detail.component';
import { SettingsComponent } from './settings/settings.component';
import { MatButtonToggleModule} from '@angular/material/button-toggle';
import { MatChipsModule} from '@angular/material/chips';
import { CommonModule } from "@angular/common";
import { DragDropModule } from '@angular/cdk/drag-drop';
import { EventSchedulerComponent } from './event-scheduler/event-scheduler.component';
import { MaterialTimePickerModule } from '@candidosales/material-time-picker';
import { PostGrnApprovalComponent } from './post-grn-approval/post-grn-approval.component';
import { PostGrnApprovalDetailComponent } from './post-grn-approval-detail/post-grn-approval-detail.component';
import { IndentApprovalComponent } from "./indent-approval/indent-approval.component";
import { IndentApprovalDetailComponent } from "./indent-approval-detail/indent-approval-detail.component";
import { CreateRequisitionListComponent } from './create-requisition-list/create-requisition-list.component';
import { DetailedCreateRequisitionComponent } from './detailed-create-requisition/detailed-create-requisition.component';
import { PurchaseStatusComponent } from './purchase-status/purchase-status.component';
import { CreatePurchaseRequisitionComponent } from './create-purchase-requisition/create-purchase-requisition.component';
import { CreateRequisitionComponent } from "./create-requisition/create-requisition.component";
import { CreateStoreIndentComponent } from './create-store-indent/create-store-indent.component';
import { DirectIbtListComponent } from './direct-ibt-list/direct-ibt-list.component';
import { CsiApprovalComponent } from './csi-approval/csi-approval.component';
import { CsiApprovalDetailComponent } from './csi-approval-detail/csi-approval-detail.component';
import { StockConversionComponent } from "./stock-conversion/stock-conversion.component";
import { StockConversionListComponent } from "./stock-conversion-list/stock-conversion-list.component";
import { DetailedScListComponent } from './detailed-sc-list/detailed-sc-list.component';
import { PiApprovalListComponent } from './pi-approval-list/pi-approval-list.component';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';
import { TaxStructureComponent } from './tax-structure/tax-structure.component';
import { CreateUserControlComponent } from './create-user-control/create-user-control.component';
import {MatRadioModule} from '@angular/material/radio';
import { NgxSliderModule } from "@angular-slider/ngx-slider";
import { ReturnToStoreComponent } from './return-to-store/return-to-store.component';
import { SpoilageComponent } from './spoilage/spoilage.component';
import { LedgerDocumentsComponent } from './ledger-documents/ledger-documents.component';
import { DetailedLedgerComponent } from './detailed-ledger/detailed-ledger.component';
import { DetailedRtvListComponent } from "./detailed-rtv-list/detailed-rtv-list.component";
import { DetailedRtvsComponent } from "./detailed-rtvs/detailed-rtvs.component";
import { RtvsComponent } from './rtvs/rtvs.component';
import { IntraBranchListComponent } from "./intra-branch-list/intra-branch-list.component";
import { DetailedIntraBranchComponent } from "./detailed-intra-branch/detailed-intra-branch.component";

@NgModule({
  declarations: [
    PackageDialogComponent,
    PythonScriptComponent,
    AppComponent,
    LoginComponent,
    LoginLayoutComponent,
    HomeLayoutComponent,
    NavigationComponent,
    HomeComponent,
    ProductionPlanningComponent,
    TableParamsComponent,
    DigiTableComponent,
    ForecastReportComponent,
    LoadingScreenComponent,
    ErrorDialogComponent,
    PageNotFoundComponent,
    PurchaseListComponent,
    OrderTableComponent,
    VendorPuchaseRequestsComponent,
    PurchaseOrdersComponent,
    VendorPurchaseOrdersComponent,
    PurchaseOrderTableComponent,
    CreatePurchaseOrderComponent,
    SimpleDialogComponent,
    InvoiceComponent,
    ReceiveOrderComponent,
    InvoiceDialogComponent,
    PrPreviewDialogComponent,
    PurchaseRequestsComponent,
    DetailedPrComponent,
    GrnComponent,
    DetailedGrnComponent,
    IbtComponent,
    IbtsComponent,
    DetailedIbtComponent,
    PreviewIbtComponent,
    InventoryListComponent,
    IndentsComponent,
    IssueIndentComponent,
    PackageDialogComponent,
    DigitoryPaginatorDirective,
    AdjustInventoryComponent,
    IndentsListComponent,
    IndentsDetailComponent,
    IndentPredictionComponent,
    InitiateRtvComponent,
    RtvListComponent,
    DetailedRtvComponent,
    InitiateRtsComponent,
    RtsListComponent,
    RtsDetailComponent,
    AdjustInvListComponent,
    AdjustInvDetailComponent,
    PurchaseSettingComponent,
    PurchaseApprovalComponent,
    PoapprovalDetailComponent,
    InfoComponent,
    IntraBranchComponent,
    PurchaseInvoiceComponent,
    DetailedPiComponent,
    CustomCarouselComponent,
    JobmonitorComponent,
    CrmQrComponent,
    EmailStatusComponent,
    ClosingComponent,
    NotifiDialogComponent,
    PriceListComponent,
    DetailedPlComponent,
    PoApprovalComponent,
    GrnapprovalDetailComponent,
    SettingsComponent,
    EventSchedulerComponent,
    PostGrnApprovalComponent,
    PostGrnApprovalDetailComponent,
    IndentApprovalComponent,
    IndentApprovalDetailComponent,
    CreateRequisitionListComponent,
    DetailedCreateRequisitionComponent,
    PurchaseStatusComponent,
    CreatePurchaseRequisitionComponent,
    CreateRequisitionComponent,
    CreateStoreIndentComponent,
    DirectIbtListComponent,
    CsiApprovalComponent,
    CsiApprovalDetailComponent,
    StockConversionComponent,
    StockConversionListComponent,
    DetailedScListComponent,
    PiApprovalListComponent,
    TaxStructureComponent,
    CreateUserControlComponent,
    ReturnToStoreComponent,
    SpoilageComponent,
    LedgerDocumentsComponent,
    DetailedLedgerComponent,
    DetailedRtvListComponent,
    DetailedRtvsComponent,
    RtvsComponent,
    IntraBranchListComponent,
    DetailedIntraBranchComponent
  ],
  imports: [
    BrowserAnimationsModule,
    ToastrModule.forRoot(),
    AngularMultiSelectModule,
    FormsModule,
    MatStepperModule,
    MatNativeDatetimeModule,
    MatDatetimepickerModule,
    MatTabsModule,
    ScrollingModule,
    AppRoutingModule,
    MatSliderModule,
    FormsModule,
    HttpClientModule,
    MatSidenavModule,
    MatToolbarModule,
    MatButtonModule,
    MatListModule,
    MatBadgeModule,
    MatAutocompleteModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatTooltipModule,
    MatSelectModule,
    MatDialogModule,
    BrowserModule,
    AppRoutingModule,
    MatComponentsModule,
    ReactiveFormsModule,
    HttpClientModule,
    FormsModule,
    Ng2SmartTableModule,
    Ng2SearchPipeModule,
    ScrollingModule,
    MaterialTimePickerModule,
    NgxDaterangepickerMd.forRoot({
      format: "DD-MM-YYYY",
      separator: " To ",
      firstDay: 0
    }),
    MatDatepickerModule,
    FlexLayoutModule,
    NgxMatSelectSearchModule,
    ImageViewerModule,
    A11yModule,
    DragDropModule,
    MatButtonToggleModule,
    MatChipsModule,
    CommonModule,
    BrowserAnimationsModule,
    MatRadioModule,
    NgxSliderModule
    // Time24to12Format
  ],
  schemas: [ CUSTOM_ELEMENTS_SCHEMA],
  providers: [
    { provide: LOCALE_ID, useValue: 'en-EN' },
    { provide: MAT_DATE_LOCALE, useValue: 'en-EN' },
    MatDatepickerModule,
    MatStepperModule,
    PackageDialogComponent,
    AuthService,
    AuthGuard,
    LoginGuard,
    MenuItemService,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: LoadingScreenInterceptor,
      multi: true
    },
    UtilsService,
    LoadingScreenService,
  ],
  bootstrap: [AppComponent],
  entryComponents: [
    ErrorDialogComponent,
    LoadingScreenComponent,
    NotifiDialogComponent,
    SimpleDialogComponent,
    InvoiceDialogComponent,
    PrPreviewDialogComponent,
    PreviewIbtComponent,
    PackageDialogComponent,
    InfoComponent
    
  ]
})
export class AppModule { }
