import { Component, OnInit, ViewChild } from '@angular/core';
import { BranchTransferService } from '../_services/branch-transfer.service';
import { AuthService, ShareDataService } from '../_services';
import { FormControl, FormGroup, FormBuilder, Validators  } from '@angular/forms';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog } from '@angular/material';
export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { UtilsService } from '../_utils/utils.service';
import { SharedFilterService } from '../_services/shared-filter.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { Router } from '@angular/router';

@Component({
  selector: 'app-ledger-documents',
  templateUrl: './ledger-documents.component.html',
  styleUrls: ['./ledger-documents.component.scss', "./../../common-dark.scss"],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
            { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
          ]
})
export class LedgerDocumentsComponent implements OnInit {
  user: any;
  ledgerForm: FormGroup;
  // @ViewChild(MatPaginator) set paginator(value: MatPaginator) {
  //   if (this.dataSource) {
  //     this.dataSource.paginator = value;
  //   }
  // };
  @ViewChild(MatPaginator) paginator: MatPaginator;
  dataSource = new MatTableDataSource();
  @ViewChild(MatSort) sort: MatSort;
  displayedColumns: string[];
  pageSizes = []
  searchText: string;
  sharedFilterData: any = {};
  private unsubscribe$ = new Subject<void>();
  getBranchData: any[]
  branchesData: any[]
  branches: any[];
    
  constructor(
    private branchTransfer: BranchTransferService,
    private auth: AuthService,
    private utils: UtilsService,
    private sharedData: ShareDataService,
    private fb: FormBuilder,
    private sharedFilterService: SharedFilterService,
    private router: Router,
    ) {

    this.user = this.auth.getCurrentUser();

    this.ledgerForm = this.fb.group({
      branchSelection: [null, Validators.required],
      startDate: [null, Validators.required],
      endDate: [null, Validators.required]
    });

    this.sharedFilterService.getLedgerFilter.subscribe(obj => 
      this.sharedFilterData = obj
      
    ); 

  //   this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
  //     this.getBranchData = val;
  //     if(this.getBranchData.length == 0){
  //       this.branches = this.user.restaurantAccess;
  //     }else if(this.getBranchData.length == 1){
  //       const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
  //       if(toSelect != this.sharedFilterData.restaurantId){
  //         this.sharedFilterData = '';
  //         this.ledgerForm.get('startDate').setValue(null)
  //         this.ledgerForm.get('endDate').setValue(null)
  //       }
  //       this.branches = this.getBranchData
  //       this.ledgerForm.get('branchSelection').setValue(toSelect);
  //       this.getSpoilageData();
  //       if(this.sharedFilterData){
  //         this.sharedFilterData.branchFlag = false;
  //       }
  //     }else{
  //       if(this.sharedFilterData.branchFlag == true){
  //         this.getSpoilageData();
  //         this.sharedFilterData.branchFlag = false;
  //       }
  //       this.branches = this.getBranchData
  //     }
  // });

  this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
    this.getBranchData = val;
    if(this.getBranchData.length == 0 ){
      this.branches = this.user.restaurantAccess;
    }else if(this.getBranchData.length == 1){        
      const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
      this.ledgerForm.get('branchSelection').setValue(toSelect);
      this.branches = this.getBranchData
      this.getSpoilageData();
    }else{
      this.branches = this.getBranchData
      this.getSpoilageData();
    }
});

  }

  ngOnInit() {
  }

  getSpoilageData(){
    if (this.sharedFilterData != '') {
      this.ledgerForm.get('branchSelection').setValue(this.sharedFilterData.restaurantId);
      this.branches = this.getBranchData
      if(this.sharedFilterData.selectedStartDate && this.sharedFilterData.selectedEndDate){
        this.ledgerForm.get('startDate').setValue(this.sharedFilterData.selectedStartDate)
        this.ledgerForm.get('endDate').setValue(this.sharedFilterData.selectedEndDate)
      }
    }  

    let params = {
      tenantId: this.user.tenantId,
      restaurantId: this.ledgerForm.value.branchSelection.restaurantIdOld,
    }
    if(this.ledgerForm.value.startDate && this.ledgerForm.value.endDate){
      params['startDate'] = this.ledgerForm.value.startDate,
      params['endDate'] = this.ledgerForm.value.endDate
    }

    this.branchTransfer.getSpoilageList(params).subscribe(res => {
      this.displayedColumns = ['id', 'user' , 'eta' , 'workArea','status']
      this.dataSource.data = res.data
      this.dataSource.sort = this.sort;
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
      this.dataSource.paginator = this.paginator;
    })
  }

  filterByDate() {
    this.getSpoilageData();
  }

  clearDate(){
    this.ledgerForm.get('startDate').setValue(null)
    this.ledgerForm.get('endDate').setValue(null)
    this.getSpoilageData();
  }

  refreshData(){
    this.getSpoilageData();
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  clearSearchText() {
    this.searchText = ''
    this.doFilter(this.searchText)
  }

  // resetForm() {
  //   // this.date = '';
  //   // this.purchaseStat.setValue('')
  //   // this.vendors.setValue('')
  //   // this.searchText = ''
  //   // this.filterKeys.status.orderStatus = 'All'
  //   // this.filterKeys.vendorDetails.vendorName = 'All'
  //   // this.searchValue = ''
  //   // this.doFilter(this.searchValue)
  //   // this.dataSource.data = this.filteredByDateList;    
  // }

  goToDetails(element){
    let inputObj = {
      restaurantId: this.ledgerForm.value.branchSelection,
      selectedStartDate: this.ledgerForm.value.startDate,
      selectedEndDate: this.ledgerForm.value.endDate,
      branchFlag: true
    }
    this.sharedFilterService.getLedgerFilter.next(inputObj);
    this.sharedData.changeLedger(element);
    this.router.navigate(['/home/<USER>']);
  }
  
}
