<div class="title">
  <button mat-button mat-raised-button style="float: right;" 
  class="button3" *ngIf="spoilageData?.status != 'Completed'" [disabled]="disableSpoilage" (click)="close()">Close Ledger</button>

  <button  mat-button mat-raised-button style="float: right;" 
  class="button3" *ngIf="spoilageData?.status == 'Completed'"  [disabled]="disableSpoilage"(click)="save()">Save</button>

  <button mat-raised-button class="button" style="float: right;" (click)="printPdf()">Print</button>

  <button mat-raised-button class="button" style="float: left; margin-left: 0px;" (click)=goBack()>
    <mat-icon>keyboard_backspace</mat-icon> Back to Spoilage List
  </button>
</div>

<div class="search-table-input fieldcontainer">
  <div class="row">
    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">Ledger ID </th>
            <td>{{ ledgerData.id }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Restaurant Name </th>
            <td>{{ ledgerData.restaurantId }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">status </th>
            <td >{{ ledgerData.status }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">IndentArea</th>
            <td>{{ ledgerData.indentArea }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Created Date</th>
            <td >{{ (ledgerData.createTs | date: "EEEE, MMMM d, y") || '-' }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">Created By</th>
            <td >{{ ledgerData.createdBy || '-' }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Closed By</th>
            <td >{{ ledgerData.closedBy || '-' }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

  <mat-card>
    <mat-card-content>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <input matInput type="text" class="outline" placeholder="Search" [(ngModel)]='searchText'
            (keyup)="doFilter($event.target.value)" />
            <mat-icon matSuffix (click)="clearSearchText()" class="closebtn">close</mat-icon>
        </mat-form-field>
      </div>

      <table #table mat-table [dataSource]="dataSource">
        <ng-container matColumnDef="itemName">
          <th mat-header-cell *matHeaderCellDef>
            <b> Item Name </b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{element.itemName}}
          </td>
          <td mat-footer-cell *matFooterCellDef class="name-cell">Total</td>
        </ng-container>

        <ng-container matColumnDef="packageName" >
          <th mat-header-cell *matHeaderCellDef>
            <b>PackageName</b>
          </th>
          <td mat-cell *matCellDef="let element">
           {{ element.packageName }} 
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="category">
          <th mat-header-cell *matHeaderCellDef>
            <b> Category </b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.category }}
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="subCategory">
          <th mat-header-cell *matHeaderCellDef>
            <b>Sub Category</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.subCategory }} 
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="Qty">
          <th mat-header-cell *matHeaderCellDef>
            <b>Spoilage Qty</b>
          </th>
          <td mat-cell *matCellDef="let element"> 
            <!-- {{ element.adjustQty }}  -->
          <input class="input1" type="number" min="0" (keyup)="getTotalPrCost($event , element)"
            [(ngModel)]="element.adjustQty" [disabled]="pr?.purchaseStatus || element.priceType"
            (focus)="focusFunctionWithOutForm(element)" (focusout)="focusOutFunctionWithOutForm(element)" />
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="stockQty">
          <th mat-header-cell *matHeaderCellDef>
            <b>stock Qty</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.instockData }} 
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef>
            <b>Adequate</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <div class="d-flex" *ngIf="element.instockData > element.adjustQty || element.instockData === element.adjustQty">
              <mat-icon style="color: #33cc33;">check_circle</mat-icon> 
            </div>
            <div class="d-flex" *ngIf="element.adjustQty > element.instockData">
              <mat-icon style="color:#ff0000;">highlight_off</mat-icon>
            </div>
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="history">
          <th mat-header-cell *matHeaderCellDef>
            <b> History </b>
          </th>
          <td mat-cell *matCellDef="let element">
            <button mat-icon-button class="delete-button" matTooltip="View Details" (click)="spoilageHistory(element)" matTooltipPosition="right">
              <mat-icon>info</mat-icon>
            </button>
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="inclTax">
          <th mat-header-cell *matHeaderCellDef>
            <b>WAC(incl.tax,etc)</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.truncateNew(element.withTaxPrice ,2)}} 
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="total">
          <th mat-header-cell *matHeaderCellDef>
            <b>Total</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.calculateAmount(element) }} 
          </td>
          <td mat-footer-cell *matFooterCellDef>{{ this.utils.truncateNew(getTotal('totalPrice'),2) }}</td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
      </table>
    </mat-card-content>
  </mat-card>

<!-- ========================================================== -->

<ng-template #openD>
	<button mat-icon-button matDialogClose="yes" matTooltip="Close" class="CloseBtn">
		<mat-icon>close</mat-icon>
	</button>
	<h2 mat-dialog-title>
		<b>Close Ledger Document</b>
	</h2>

	  <div>
      <mat-dialog-content class="mat-typography">
        <div>
          <p class="d-flex justify-content-center dataMessage mb-3"> You are about to close this ledger document would you like to Proceed with inventory reduction for the item listed? this action is irreversible </p>
        </div>
        <div class="row justify-content-center">
          <button mat-raised-button class="button3 mb-2 mx-2" matDialogClose="yes" (click)="sendReqAdequate()">
            yes, proceed
          </button>
  
          <button mat-raised-button class="button3 mb-2 mx-2" matDialogClose="yes" (click)="closeAdequateLedger()">
            Skip Reduction, proceed
          </button>
        </div>
      </mat-dialog-content>
	  </div>
</ng-template>

<ng-template #openConformationDialog>
	<button mat-icon-button matDialogClose="yes" matTooltip="Close" class="CloseBtn">
		<mat-icon>close</mat-icon>
	</button>
	<h2 mat-dialog-title>
		<b>Inadequate Stock Detected</b>
	</h2>

	  <div>
		<mat-dialog-content class="mat-typography">
			<div class="row justify-content-center">
        <div>
          <p class="dataMessage">Stock discrepancies found. Should we remove them and proceed with the ledger? </p>  
        </div>
      </div>
		</mat-dialog-content>
	  </div>

    <mat-dialog-actions align='center'>
      <div class="reqBtn">
				<button mat-raised-button class="button3 mb-2 mx-2" matDialogClose="yes" (click)="sendReqInAdequate()">
          Yes & Close Ledger
				</button>

        <button mat-raised-button class="mb-2 mx-2" matDialogClose="yes" (click)="closeInAdequateLedger()">
          Go Back
        </button>
			</div>
		</mat-dialog-actions>
</ng-template>

<ng-template #spoilageDialog>
  <button mat-icon-button matDialogClose="yes" matTooltip="Close" (click)="closeDialog()" class="CloseBtn">
    <mat-icon>close</mat-icon>
  </button>

  <h2 mat-dialog-title>
    <b>SPOILAGE HISTORY</b>
  </h2>

  <mat-dialog-content class="mat-typography">
    <div class="col">
      <section class="example-container-2 mat-elevation-z8"> 
        <table #table mat-table [dataSource]="spoilageDataSource" matSortDirection="desc" matSort>
          <ng-container matColumnDef="index">
            <th mat-header-cell *matHeaderCellDef class="spoilage"><b class="topItemkey">S.No</b></th>
            <td mat-cell *matCellDef="let element; let i = index" class="spoilage">
              {{ i + 1 }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>
          
          <ng-container matColumnDef="creator">
            <th mat-header-cell *matHeaderCellDef class="spoilage">
              <b class="topItemkey">CREATED BY</b>
            </th>
            <td mat-cell *matCellDef="let element" class="spoilage">
              {{ element.createdBy || '-' }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="createTs">
            <th mat-header-cell *matHeaderCellDef class="spoilage">
              <b class="topItemkey">CREATED DATE</b>
            </th>
            <td mat-cell *matCellDef="let element" class="spoilage">
              {{ element.createTs | date:'dd-MM-yyyy' || '-' }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="qty">
            <th mat-header-cell *matHeaderCellDef class="spoilage">
              <b class="topItemkey">QUANTITY</b>
            </th>
            <td mat-cell *matCellDef="let element" class="spoilage">
              {{ element.qty || '-' }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>
          
          <tr mat-header-row *matHeaderRowDef="spoilageDataColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: spoilageDataColumns"></tr>
          <tr mat-footer-row *matFooterRowDef="spoilageDataColumns"></tr>
        </table>
      </section>
      <div class="dataMessage" *ngIf="spoilageDataSource?.data.length == 0"> No Spoilage Data Available </div>
      <mat-paginator [showTotalPages]="5" [pageSize]="5" [pageSizeOptions]="pageSizes"></mat-paginator>    
    </div>
  </mat-dialog-content>
</ng-template>